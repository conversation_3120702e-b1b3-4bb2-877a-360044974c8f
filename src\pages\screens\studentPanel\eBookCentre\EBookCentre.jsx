'use client';

import { useState } from 'react';
import { motion, AnimatePresence, useMotionValue } from 'framer-motion';
import {
  BookOpen,
  Download,
  Eye,
  X,
  Search,
  FileText,
  Bookmark,
  Star,
  ChevronLeft,
  ChevronRight,
  ZoomIn,
  ZoomOut,
  RotateCw,
  Grid3X3,
  List
} from 'lucide-react';

const EBookCentre = () => {
  const [selectedSubject, setSelectedSubject] = useState('All');
  const [selectedMaterial, setSelectedMaterial] = useState(null);
  const [viewMode, setViewMode] = useState('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('date');
  const [currentPage, setCurrentPage] = useState(1);
  const [zoomLevel, setZoomLevel] = useState(100);

  const studyMaterials = [
    {
      id: 1,
      title: 'Human Physiology Complete Guide',
      type: 'PDF',
      size: '3.2 MB',
      date: '2025-06-20',
      category: 'NEET',
      subject: 'Biology',
      pages: 156,
      rating: 4.8,
      downloads: 1250,
      isBookmarked: true,
      thumbnail:
        'https://media.gettyimages.com/id/117613065/photo/human-brain.jpg?s=612x612&w=0&k=20&c=ELaqV6ax3V7cxKo8xPjtk18Syu7Qftfzh9iijjvEHsE=',
      description:
        'Comprehensive guide covering all aspects of human physiology for NEET preparation.'
    },
    {
      id: 2,
      title: 'Mechanics Fundamentals',
      type: 'eBook',
      size: '4.5 MB',
      date: '2025-06-18',
      category: 'JEE',
      subject: 'Physics',
      pages: 203,
      rating: 4.9,
      downloads: 2100,
      isBookmarked: false,
      thumbnail:
        'https://media.geeksforgeeks.org/wp-content/uploads/20240515152053/Mechanics-Physics.webp',
      description: 'Master the fundamentals of mechanics with detailed explanations and examples.'
    },
    {
      id: 3,
      title: 'Organic Chemistry Reactions',
      type: 'PDF',
      size: '2.8 MB',
      date: '2025-06-15',
      category: 'NEET',
      subject: 'Chemistry',
      pages: 89,
      rating: 4.7,
      downloads: 890,
      isBookmarked: true,
      thumbnail:
        'https://img.freepik.com/premium-vector/organic-chemistry-outline-outline-icons_104589-812.jpg?semt=ais_hybrid&w=740',
      description: 'Essential organic chemistry reactions with mechanisms and practice problems.'
    },
    {
      id: 4,
      title: 'Advanced Calculus',
      type: 'PDF',
      size: '3.9 MB',
      date: '2025-06-12',
      category: 'JEE',
      subject: 'Mathematics',
      pages: 178,
      rating: 4.6,
      downloads: 1560,
      isBookmarked: false,
      thumbnail:
        'https://st5.depositphotos.com/2094567/77866/i/450/depositphotos_778669226-stock-photo-advanced-mathematical-integrals-formulas-highlighting.jpg',
      description: 'Advanced calculus concepts with step-by-step solutions and applications.'
    },
    {
      id: 5,
      title: 'Thermodynamics Laws',
      type: 'eBook',
      size: '2.1 MB',
      date: '2025-06-10',
      category: 'NEET',
      subject: 'Physics',
      pages: 67,
      rating: 4.5,
      downloads: 720,
      isBookmarked: true,
      thumbnail:
        'https://t4.ftcdn.net/jpg/12/53/31/99/360_F_1253319905_Q3CM7ZqQCz6cxMW2kY7F4lyH1Cz3qfGj.jpg',
      description: 'Understanding thermodynamics laws with real-world applications.'
    },
    {
      id: 6,
      title: 'Physical Chemistry Concepts',
      type: 'PDF',
      size: '3.4 MB',
      date: '2025-06-08',
      category: 'JEE',
      subject: 'Chemistry',
      pages: 134,
      rating: 4.8,
      downloads: 1340,
      isBookmarked: false,
      thumbnail:
        'https://cache.careers360.mobi/media/article_images/2024/11/28/Some_basic_concepts_of_chemistry.png',
      description: 'Core physical chemistry concepts with numerical problems and solutions.'
    }
  ];

  const subjects = ['All', 'Biology', 'Chemistry', 'Physics', 'Mathematics'];

  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);

  const filteredMaterials = studyMaterials
    .filter((material) => selectedSubject === 'All' || material.subject === selectedSubject)
    .filter((material) => material.title.toLowerCase().includes(searchTerm.toLowerCase()))
    .sort((a, b) => {
      if (sortBy === 'date') return new Date(b.date).getTime() - new Date(a.date).getTime();
      if (sortBy === 'rating') return b.rating - a.rating;
      if (sortBy === 'downloads') return b.downloads - a.downloads;
      return a.title.localeCompare(b.title);
    });

  const PDFViewer = ({ material }) => {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="bg-gray-100 rounded-xl overflow-hidden"
        style={{ height: '500px' }}>
        {/* PDF Viewer Header */}
        <div className="bg-white border-b border-gray-200 p-4 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <span className="text-sm text-gray-600">
              Page {currentPage} of {material.pages}
            </span>
            <div className="flex items-center gap-2">
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                className="p-2 hover:bg-gray-100 rounded-lg"
                disabled={currentPage === 1}>
                <ChevronLeft size={16} />
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setCurrentPage(Math.min(material.pages, currentPage + 1))}
                className="p-2 hover:bg-gray-100 rounded-lg"
                disabled={currentPage === material.pages}>
                <ChevronRight size={16} />
              </motion.button>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">{zoomLevel}%</span>
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setZoomLevel(Math.max(50, zoomLevel - 25))}
              className="p-2 hover:bg-gray-100 rounded-lg">
              <ZoomOut size={16} />
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => setZoomLevel(Math.min(200, zoomLevel + 25))}
              className="p-2 hover:bg-gray-100 rounded-lg">
              <ZoomIn size={16} />
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.1, rotate: 90 }}
              whileTap={{ scale: 0.9 }}
              className="p-2 hover:bg-gray-100 rounded-lg">
              <RotateCw size={16} />
            </motion.button>
          </div>
        </div>

        {/* PDF Content Area */}
        <div className="flex-1 flex items-center justify-center p-8 bg-gray-50">
          <motion.div
            className="bg-white shadow-lg rounded-lg p-8 max-w-2xl w-full"
            style={{ transform: `scale(${zoomLevel / 100})` }}
            animate={{ scale: zoomLevel / 100 }}
            transition={{ duration: 0.3 }}>
            <div className="space-y-4">
              <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2"></div>
              <div className="space-y-2 mt-6">
                <div className="h-3 bg-gray-100 rounded animate-pulse"></div>
                <div className="h-3 bg-gray-100 rounded animate-pulse"></div>
                <div className="h-3 bg-gray-100 rounded animate-pulse w-5/6"></div>
              </div>
              <div className="text-center text-gray-500 mt-8">
                <FileText size={48} className="mx-auto mb-2 opacity-50" />
                <p>PDF Content Preview</p>
                <p className="text-sm">
                  Page {currentPage} of {material.pages}
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      </motion.div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50/50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Modern Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12">
          <motion.div className="inline-flex items-center gap-3 mb-4" whileHover={{ scale: 1.02 }}>
            <div className="w-12 h-12 bg-student rounded-2xl flex items-center justify-center">
              <BookOpen className="text-white" size={24} />
            </div>
            <h1 className="text-4xl font-bold text-[var(--color-student)]">E-Book Center</h1>
          </motion.div>
          <p className="text-gray-600 text-lg">Your digital library for NEET & JEE preparation</p>
        </motion.div>

        {/* Search and Filter Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-[var(--color-student)] rounded-2xl p-6 mb-8 shadow-sm border border-gray-100">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={18}
              />
              <input
                type="text"
                placeholder="Search books..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-gray-50 rounded-xl border-0 focus:ring-2 focus:ring-student/20 focus:bg-white transition-all"
              />
            </div>

            {/* Filters */}
            <div className="flex items-center gap-4">
              {/* Subject Filter */}
              <div className="flex gap-2">
                {subjects.map((subject) => (
                  <motion.button
                    key={subject}
                    onClick={() => setSelectedSubject(subject)}
                    className={`px-4 py-2 hover:cursor-pointer rounded-xl text-sm font-medium transition-all ${
                      selectedSubject === subject
                        ? 'bg-[var(--color-counselor)] text-white'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}>
                    {subject}
                  </motion.button>
                ))}
              </div>

              {/* Sort */}
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-4 py-2 bg-gray-100 rounded-xl hover:cursor-pointer border-0 focus:ring-2 focus:ring-student/20">
                <option value="date">Latest</option>
                <option value="rating">Rating</option>
                <option value="downloads">Popular</option>
                <option value="title">A-Z</option>
              </select>

              {/* View Mode */}
              <div className="flex bg-gray-100 rounded-xl p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-lg transition-all ${viewMode === 'grid' ? 'bg-white shadow-sm' : ''}`}>
                  <Grid3X3
                    size={16}
                    className={viewMode === 'grid' ? 'text-student' : 'text-gray-500'}
                  />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-lg transition-all ${viewMode === 'list' ? 'bg-white shadow-sm' : ''}`}>
                  <List
                    size={16}
                    className={viewMode === 'list' ? 'text-student' : 'text-gray-500'}
                  />
                </button>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Books Grid/List */}
        <AnimatePresence mode="wait">
          <motion.div
            key={`${selectedSubject}-${viewMode}-${searchTerm}-${sortBy}`}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className={`grid gap-6 ${
              viewMode === 'grid'
                ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                : 'grid-cols-1'
            }`}>
            {filteredMaterials.map((material, index) => (
              <motion.div
                key={material.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`bg-white rounded-2xl overflow-hidden border border-gray-100 hover:border-gray-200 transition-all group ${
                  viewMode === 'list' ? 'flex gap-6' : ''
                }`}
                whileHover={{
                  y: -4,
                  boxShadow: '0 20px 40px -12px rgba(0,0,0,0.1)'
                }}>
                {/* Thumbnail */}
                <div className={`relative ${viewMode === 'list' ? 'w-32 flex-shrink-0' : ''}`}>
                  <img
                    src={material.thumbnail || '/placeholder.svg'}
                    alt={material.title}
                    className={`object-cover ${viewMode === 'list' ? 'w-full h-40' : 'w-full h-48'}`}
                  />
                  <div className="absolute top-3 right-3">
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      className={`p-2 rounded-full backdrop-blur-sm ${
                        material.isBookmarked
                          ? 'bg-yellow-400 text-white'
                          : 'bg-white/80 text-gray-600'
                      }`}>
                      <Bookmark size={14} fill={material.isBookmarked ? 'currentColor' : 'none'} />
                    </motion.button>
                  </div>
                  <div className="absolute bottom-3 left-3 bg-student text-white px-2 py-1 rounded-lg text-xs font-medium">
                    {material.type}
                  </div>
                </div>

                {/* Content */}
                <div className="p-6 flex-1">
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="font-bold text-gray-900 line-clamp-2 leading-tight">
                      {material.title}
                    </h3>
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full ml-2">
                      {material.category}
                    </span>
                  </div>

                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">{material.description}</p>

                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-1">
                        <Star size={12} className="text-yellow-400 fill-current" />
                        <span>{material.rating}</span>
                      </div>
                      <span>{material.downloads} downloads</span>
                      <span>{material.pages} pages</span>
                    </div>
               
                  </div>

                  <div className="flex gap-2">
                    <motion.button
                      onClick={() => setSelectedMaterial(material)}
                      className="flex-1 bg-student/10 text-student hover:cursor-pointer py-2.5 px-3 rounded-xl font-medium hover:bg-student/20 transition-all flex items-center justify-center gap-2"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}>
                      <Eye size={14} />
                      View
                    </motion.button>
                    <motion.button
                      className="flex-1 bg-student text-white py-2.5 px-3  rounded-xl font-medium hover:bg-student/90 transition-all flex items-center justify-center gap-2"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}>
                      <Download size={14} />
                      Download
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </AnimatePresence>

        {/* No Results */}
        {filteredMaterials.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12">
            <BookOpen size={48} className="mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500 text-lg">No books found matching your criteria</p>
          </motion.div>
        )}

        {/* PDF Viewer Modal */}
        <AnimatePresence>
          {selectedMaterial && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50"
              onClick={() => setSelectedMaterial(null)}>
              <motion.div
                initial={{ opacity: 0, scale: 0.9, y: 20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.9, y: 20 }}
                className="bg-white rounded-3xl max-w-5xl w-full max-h-[90vh] overflow-hidden"
                onClick={(e) => e.stopPropagation()}>
                {/* Modal Header */}
                <div className="flex items-center justify-between p-6 border-b border-gray-100">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900">{selectedMaterial.title}</h2>
                    <p className="text-gray-600">{selectedMaterial.description}</p>
                  </div>
                  <motion.button
                    onClick={() => setSelectedMaterial(null)}
                    className="p-2 hover:bg-gray-100 rounded-xl transition-colors"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}>
                    <X size={20} />
                  </motion.button>
                </div>

                {/* PDF Viewer */}
                <div className="p-6">
                  <PDFViewer material={selectedMaterial} />
                </div>

                {/* Modal Footer */}
                <div className="flex items-center justify-between p-6 border-t border-gray-100 bg-gray-50">
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <span>Size: {selectedMaterial.size}</span>
                    <span>Pages: {selectedMaterial.pages}</span>
                    <span>Type: {selectedMaterial.type}</span>
                  </div>
                  <motion.button
                    className="bg-student text-white px-6 py-2.5 rounded-xl font-medium hover:bg-student/90 transition-all flex items-center gap-2"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}>
                    <Download size={16} />
                    Download PDF
                  </motion.button>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default EBookCentre;
