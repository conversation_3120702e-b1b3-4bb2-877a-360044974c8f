import { CenterliveViewerApi } from '../../../../redux/api/api';

const initialState = {
  centerLiveViewerData: null
};

export const centerTraineeLiveSlice = CenterliveViewerApi.injectEndpoints({
  endpoints: (builder) => ({
    getCenterLiveViewer: builder.query({
      query: () => ({
        url: '/active-streams',
        method: 'GET',
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Center Live Viewer Data:', response);

        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['CenterLiveViewer']
    }),
    joinLiveStream: builder.mutation({
      query: (sessionData) => {
        // Get user_id from sessionStorage
        const userId = sessionStorage.getItem('userId');

        // Prepare the request body with required fields
        const requestBody = {
          session_id: sessionData.session_id,
          user_id: userId,
          teacher_id: sessionData.teacher_id
        };

        console.log('Join Stream Request Body:', requestBody);

        return {
          url: '/api/livekit/join',
          method: 'POST',
          body: requestBody,
          responseHandler: async (res) => res.json()
        };
      },
      transformResponse: (response) => {
        console.log('Join Stream Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      invalidatesTags: ['CenterLiveViewer']
    }),


    startTranslationSession: builder.mutation({
      query: (translationData) => {
        console.log('Start Translation Request:', translationData);

        return {
          url: '/api/translate/start-session',
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: translationData,
          responseHandler: async (res) => res.json()
        };
      },
      transformResponse: (response) => {
        console.log('Start Translation Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      })
    }),
    stopTranslationSession: builder.mutation({
      query: (sessionData) => {
        console.log('Stop Translation Request:', sessionData);

        return {
          url: '/api/translate/stop-session',
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: sessionData,
          responseHandler: async (res) => res.json()
        };
      },
      transformResponse: (response) => {
        console.log('Stop Translation Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      })
    }),

    // Chat functionality endpoints
    sendChatMessage: builder.mutation({
      query: (messageData) => {
        console.log('Send Chat Message Request:', messageData);

        return {
          url: '/api/chat/send',
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${sessionStorage.getItem('token')}`
          },
          body: messageData,
          responseHandler: async (res) => res.json()
        };
      },
      transformResponse: (response) => {
        console.log('Send Chat Message Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      })
    }),

    getChatHistory: builder.query({
      query: (sessionId) => {
        console.log('Get Chat History Request for session:', sessionId);

        return {
          url: `/api/chat/history/${sessionId}`,
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${sessionStorage.getItem('token')}`
          },
          responseHandler: async (res) => res.json()
        };
      },
      transformResponse: (response) => {
        console.log('Get Chat History Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: (result, error, sessionId) => [
        { type: 'ChatHistory', id: sessionId }
      ]
    })
  })
});




export const {
  useLazyGetCenterLiveViewerQuery,
  useJoinLiveStreamMutation,
  useStartTranslationSessionMutation,
  useStopTranslationSessionMutation,
  useSendChatMessageMutation,
  useLazyGetChatHistoryQuery
} = centerTraineeLiveSlice;
  