import { useEffect, useCallback, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  User,
  School,
  Phone,
  Mail,
  MailCheck,
  University,
  BookOpen,
  Star,
  Award,
  Home,
  RotateCw,
  Hash,
  Info,
  Calendar,
  Bookmark,
  BarChart2,
  ChevronRight,
  MessageSquare,
  Video,
  FileText,
  Rocket,
  Bot
} from 'lucide-react';
import { useDispatch, useSelector } from 'react-redux';
import {
  setOnBroadingAssessmentStatus,
  useCheckOnBroadingAssessmentStatusMutation
} from '../onBroadingAssesment/onBroadingAssessment.slice';
import { useGetStudentsQuery } from './students.Slice';
import OnBroadingAssessment from '../onBroadingAssesment/OnBroadingAssessment';

const StudentsDashboard = () => {
  const [checkOnBroadingAssessmentStatus] = useCheckOnBroadingAssessmentStatusMutation();
  const dispatch = useDispatch();
  const studentId = sessionStorage.getItem('userId');

  // Fetch student and teacher data
  const {
    data: studentsData,
    isLoading,
    isError,
    error
  } = useGetStudentsQuery(undefined, {
    skip: !studentId
  });

  const onBroadingAssessmentData = useSelector(
    (state) => state.onBroadingAssessment.onBroadingAssessmentStatus
  );

  const [isSkipped, setIsSkipped] = useState(sessionStorage.getItem('isSkip') === 'true');

  const handleCheckAssessmentStatus = useCallback(async () => {
    try {
      const studentId = sessionStorage.getItem('userId');
      if (!studentId) return;

      const res = await checkOnBroadingAssessmentStatus({
        student_id: studentId
      }).unwrap();

      const sessionSkip = sessionStorage.getItem('isSkip');

      if (res.assessment_required && !res.assessment_completed && sessionSkip !== 'true') {
        sessionStorage.setItem('isSkip', 'false');
        setIsSkipped(false);
      }

      dispatch(setOnBroadingAssessmentStatus(res));
    } catch (error) {
      console.error('Error checking assessment status:', error);
    }
  }, [checkOnBroadingAssessmentStatus, dispatch]);

  useEffect(() => {
    handleCheckAssessmentStatus();
  }, [handleCheckAssessmentStatus]);

  const handleSkip = () => {
    setIsSkipped(true);
    sessionStorage.setItem('isSkip', 'true');
  };

  const shouldShowAssessment =
    onBroadingAssessmentData?.assessment_required &&
    !onBroadingAssessmentData?.assessment_completed &&
    !isSkipped;

  // Student details
  const user = studentsData?.student || {
    username: sessionStorage.getItem('name') || 'Unknown',
    center_code: sessionStorage.getItem('centercode') || 'Unknown',
    phone: sessionStorage.getItem('phone') || 'Unknown',
    course: 'Unknown'
  };

  const containerVariants = {
    hidden: { opacity: 0, scale: 0.9, y: 100 },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: { duration: 0.8, ease: 'easeOut', staggerChildren: 0.2 }
    }
  };

  const childVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
  };

  // Dashboard widgets with matching color scheme
  const dashboardWidgets = [
    {
      id: 1,
      title: '24/7 Chatbot Support',
      stat: '150+ Queries',
      description: 'Get instant help anytime with our AI chatbot!',
      icon: '💬',
      color: 'from-[#2563eb] to-[#1d4ed8]',
      bgColor: 'bg-[#2563eb]/5',
      borderColor: 'border-[#2563eb]/20',
      textColor: 'text-[#2563eb]'
    },
    {
      id: 2,
      title: 'E-Book Center',
      stat: '200+ Books',
      description: 'Access a vast library of e-books for learning.',
      icon: '📚',
      color: 'from-[#2563eb] to-[#1d4ed8]',
      bgColor: 'bg-[#2563eb]/5',
      borderColor: 'border-[#2563eb]/20',
      textColor: 'text-[#2563eb]'
    },
    {
      id: 3,
      title: 'Recordings',
      stat: '75 Videos',
      description: 'Watch recorded lectures at your convenience.',
      icon: '🎥',
      color: 'from-[#2563eb] to-[#1d4ed8]',
      bgColor: 'bg-[#2563eb]/5',
      borderColor: 'border-[#2563eb]/20',
      textColor: 'text-[#2563eb]'
    },
    {
      id: 4,
      title: 'Create Your Own Test',
      stat: '50 Tests',
      description: 'Design custom tests to assess your skills.',
      icon: '📝',
      color: 'from-[#2563eb] to-[#1d4ed8]',
      bgColor: 'bg-[#2563eb]/5',
      borderColor: 'border-[#2563eb]/20',
      textColor: 'text-[#2563eb]'
    },
    {
      id: 5,
      title: 'Booster Module',
      stat: '30 Exercises',
      description: 'Boost your performance with targeted exercises.',
      icon: '🚀',
      color: 'from-[#2563eb] to-[#1d4ed8]',
      bgColor: 'bg-[#2563eb]/5',
      borderColor: 'border-[#2563eb]/20',
      textColor: 'text-[#2563eb]'
    },
    {
      id: 6,
      title: 'AI Tutor',
      stat: '100+ Sessions',
      description: 'Personalized learning with an AI tutor.',
      icon: '🤖',
      color: 'from-[#2563eb] to-[#1d4ed8]',
      bgColor: 'bg-[#2563eb]/5',
      borderColor: 'border-[#2563eb]/20',
      textColor: 'text-[#2563eb]'
    }
  ];

  const getInitials = (firstName, lastName) => {
    return `${firstName?.charAt(0) || ''}${lastName?.charAt(0) || ''}`.toUpperCase();
  };

  const getRatingColor = (rating) => {
    if (rating >= 4.8) return 'bg-green-100 text-green-800';
    if (rating >= 4.5) return 'bg-[#2563eb]/10 text-[#2563eb]';
    return 'bg-yellow-100 text-yellow-800';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 p-4 md:p-8">
      {shouldShowAssessment && <OnBroadingAssessment isSkip={handleSkip} />}
      <div className="relative max-w-7xl mx-auto space-y-12 z-10">
        {/* Welcome Header with Animated Gradient */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center pt-8 pb-12">
          <motion.div
            className="inline-block"
            animate={{
              backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: 'linear'
            }}
            style={{
              backgroundSize: '300% 300%',
              WebkitBackgroundClip: 'text',
              backgroundClip: 'text',
              color: 'transparent'
            }}>
            <h1 className="text-4xl md:text-5xl text-[var(--color-student)] font-bold mb-3">
              Welcome back, {user.first_name || user.username}!
            </h1>
          </motion.div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            "Education is the most powerful weapon which you can use to change the world."
          </p>

          {/* Animated Progress Indicator */}
          <motion.div
            className="mt-5 mx-auto  max-w-md bg-[var(--color-counselor)]
             backdrop-blur-sm rounded-full p-1 mb-0 shadow-inner"
            initial={{ scaleX: 0 }}
            animate={{ scaleX: 1 }}
            transition={{ delay: 0.5, duration: 1 }}>
            <motion.div
              className="h-1 rounded-full bg-bg-[var(--color-counselor)]"
              initial={{ width: 0 }}
              animate={{ width: '65%' }}
              transition={{ delay: 1, duration: 1.5 }}
            />
          </motion.div>
        </motion.div>

        {/* Student Profile Card - Neumorphic Design with 3D Tilt */}
        {!isLoading && !isError && studentsData?.student && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="relative  group">
            <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-blue-100 to-purple-100 shadow-lg  transform group-hover:scale-105 transition-all duration-500" />
            <div className="relative bg-white/80 backdrop-blur-sm hover:cursor-pointer rounded-3xl overflow-hidden border border-white/20 shadow-xl">
              <div className="p-8">
                <div className="flex flex-col md:flex-row items-center gap-8">
                  {/* Avatar with Floating Animation */}
                  <motion.div
                    className="relative"
                    animate={{
                      y: [0, -15, 0]
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: 'easeInOut'
                    }}>
                    <div className="h-28 w-28 rounded-2xl bg-[var(--color-student)] flex items-center justify-center text-[var(--color-counselor)] text-3xl font-bold shadow-lg">
                      {getInitials(user.first_name, user.last_name)}
                    </div>
                    <motion.div
                      className="absolute -bottom-3 -right-3 h-12 w-12 bg-white rounded-xl flex items-center justify-center shadow-md border-2 border-blue-200"
                      animate={{
                        rotate: [0, 360],
                        scale: [1, 1.1, 1]
                      }}
                      transition={{
                        duration: 8,
                        repeat: Infinity,
                        ease: 'linear'
                      }}>
                      <Award className="text-blue-600" size={20} />
                    </motion.div>
                  </motion.div>

                  <div className="text-center md:text-left">
                    <h2 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-purple-600">
                      {user.first_name} {user.last_name}
                    </h2>
                    <div className="mt-3 inline-flex items-center text-white px-4 py-1.5 rounded-full bg-[var(--color-counselor)] text-sm font-medium">
                      <BookOpen size={16} className="mr-2" /> {user.course}
                    </div>
                  </div>
                </div>

                {/* Info Cards with Hover Effects */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-10">
                  <motion.div
                    className="bg-white p-6 rounded-xl border border-gray-100 shadow-sm hover:shadow-md transition-all"
                    whileHover={{ y: -5 }}>
                    <div className="flex items-center gap-4 mb-4">
                      <div className="p-3 rounded-lg bg-blue-50 text-[var(--color-counselor)]">
                        <User size={20} />
                      </div>
                      <h3 className="text-lg font-semibold">Personal Info</h3>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3 text-gray-700">
                        <Mail size={18} className="text-blue-500" />
                        <span className="truncate">{user.student_email}</span>
                      </div>
                      <div className="flex items-center gap-3 text-gray-700">
                        <Phone size={18} className="text-blue-500" />
                        <span>{user.phone}</span>
                      </div>
                    </div>
                  </motion.div>

                  <motion.div
                    className="bg-white p-6 rounded-xl border border-gray-100 shadow-sm hover:shadow-md transition-all"
                    whileHover={{ y: -5 }}>
                    <div className="flex items-center gap-4 mb-4">
                      <div className="p-3 rounded-lg bg-purple-50 text-[var(--color-counselor)]">
                        <School size={20} />
                      </div>
                      <h3 className="text-lg font-semibold">Education</h3>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3 text-gray-700">
                        <University size={18} className="text-blue-500" />
                        <span>{user.center_name}</span>
                      </div>
                      <div className="flex items-center gap-3 text-gray-700">
                        <Hash size={18} className="text-blue-500" />
                        <span>Center Code: {user.center_code}</span>
                      </div>
                      <div className="flex items-center gap-3 text-gray-700">
                        <MailCheck size={18} className="text-blue-500" />
                        <span>Center Email: {user.center_email}</span>
                        
                      </div>
                    </div>
                  </motion.div>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {!isLoading && !isError && studentsData?.faculty?.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="space-y-10">
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 inline-flex items-center gap-3">
                <span style={{ color: 'var(--color-teacher)' }}>Your Faculty</span>
                <motion.div
                  animate={{
                    x: [0, 5, 0],
                    transition: {
                      duration: 2,
                      repeat: Infinity
                    }
                  }}>
                  <User size={28} style={{ color: 'var(--color-counselor)' }} />
                </motion.div>
              </h2>
              <p className="text-gray-600 mt-2 max-w-2xl mx-auto">Meet your faculty members</p>
            </div>

            {/* Faculty Cards in a Single Row (Limited to 3) */}
            <div className="flex flex-row gap-6 justify-center">
              {studentsData.faculty.slice(0, 3).map((faculty, facultyIndex) => (
                <motion.div
                  key={faculty.id}
                  className="relative flex-shrink-0 w-80"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{
                    opacity: 1,
                    x: 0,
                    transition: { delay: 0.1 * facultyIndex }
                  }}
                  whileHover={{
                    y: -10,
                    transition: { duration: 0.2 }
                  }}
                  style={{
                    zIndex: 3 - facultyIndex
                  }}>
                  <div
                    className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 transform hover:shadow-xl transition-all duration-300 h-full flex flex-col"
                    style={{ borderColor: 'rgba(var(--color-teacher-rgb), 0.2)' }}>
                    <div className="p-6 flex-1">
                      <div className="flex items-start gap-4">
                        <div className="relative">
                          <div
                            className="h-14 w-14 rounded-xl flex items-center justify-center text-white font-bold"
                            style={{ backgroundColor: 'var(--color-counselor)' }}>
                            {getInitials(faculty.first_name, faculty.last_name)}
                          </div>
                          <motion.div
                            className="absolute -bottom-2 -right-2 h-6 w-6 bg-white rounded-full flex items-center justify-center shadow-sm border"
                            style={{ borderColor: 'rgba(var(--color-teacher-rgb), 0.3)' }}
                            animate={{
                              rotate: [0, 360],
                              transition: {
                                duration: 10,
                                repeat: Infinity,
                                ease: 'linear'
                              }
                            }}>
                            <Star size={12} style={{ color: 'var(--color-teacher)' }} />
                          </motion.div>
                        </div>
                        <div className="flex-1">
                          <h4 className="font-bold text-lg text-gray-900">
                            {faculty.first_name} {faculty.last_name}
                          </h4>
                          <p className="text-sm text-gray-600">{faculty.email}</p>
                        </div>
                      </div>

                      <div className="mt-6 grid grid-cols-2 gap-3">
                        <div
                          className="rounded-lg p-3"
                          style={{ backgroundColor: 'rgba(var(--color-teacher-rgb), 0.05)' }}>
                          <p className="text-xs text-gray-500 mb-1">Contact</p>
                          <p style={{ color: 'var(--color-teacher)' }} className="font-medium">
                            {faculty.phone}
                          </p>
                        </div>
                        <div
                          className="rounded-lg p-3"
                          style={{ backgroundColor: 'rgba(var(--color-teacher-rgb), 0.05)' }}>
                          <p className="text-xs text-gray-500 mb-1">Role</p>
                          <p style={{ color: 'var(--color-teacher)' }} className="font-medium">
                            Faculty
                          </p>
                        </div>
                      </div>
                    </div>
                    <div
                      className="px-6 py-3 border-t"
                      style={{
                        backgroundColor: 'rgba(var(--color-teacher-rgb), 0.03)',
                        borderColor: 'rgba(var(--color-teacher-rgb), 0.1)'
                      }}></div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
        {/* Teachers Section - Three Cards Side by Side with Subject */}
        {!isLoading && !isError && studentsData?.kota_teachers?.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="space-y-10">
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 inline-flex items-center gap-3">
                <span style={{ color: 'var(--color-teacher)' }}>Your Instructors</span>
                <motion.div
                  animate={{
                    x: [0, 5, 0],
                    transition: {
                      duration: 2,
                      repeat: Infinity
                    }
                  }}>
                  <User size={28} style={{ color: 'var(--color-counselor)' }} />
                </motion.div>
              </h2>
              <p className="text-gray-600 mt-2 max-w-2xl mx-auto">
                Meet your expert instructors organized by subject specialization
              </p>
            </div>

            {/* Teacher Cards in a Single Row (Limited to 3) */}
            <div className="flex flex-row gap-6 justify-center">
              {studentsData.kota_teachers.slice(0, 3).map((teacher, teacherIndex) => (
                <motion.div
                  key={teacher.id}
                  className="relative flex-shrink-0 w-80"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{
                    opacity: 1,
                    x: 0,
                    transition: { delay: 0.1 * teacherIndex }
                  }}
                  whileHover={{
                    y: -10,
                    transition: { duration: 0.2 }
                  }}
                  style={{
                    zIndex: 3 - teacherIndex
                  }}>
                  <div
                    className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 transform hover:shadow-xl transition-all duration-300 h-full flex flex-col"
                    style={{ borderColor: 'rgba(var(--color-teacher-rgb), 0.2)' }}>
                    <div className="p-6 flex-1">
                      <div className="flex items-start gap-4">
                        <div className="relative">
                          <div
                            className="h-14 w-14 rounded-xl flex items-center justify-center text-white font-bold"
                            style={{ backgroundColor: 'var(--color-counselor)' }}>
                            {getInitials(teacher.first_name, teacher.last_name)}
                          </div>
                          <motion.div
                            className="absolute -bottom-2 -right-2 h-6 w-6 bg-white rounded-full flex items-center justify-center shadow-sm border"
                            style={{ borderColor: 'rgba(var(--color-teacher-rgb), 0.3)' }}
                            animate={{
                              rotate: [0, 360],
                              transition: {
                                duration: 10,
                                repeat: Infinity,
                                ease: 'linear'
                              }
                            }}>
                            <Star size={12} style={{ color: 'var(--color-teacher)' }} />
                          </motion.div>
                        </div>
                        <div className="flex-1">
                          <h4 className="font-bold text-lg text-gray-900">
                            {teacher.first_name} {teacher.last_name}
                          </h4>
                          <p className="text-sm text-gray-600">{teacher.course}</p>
                          <div className="mt-1 inline-flex items-center px-3 py-1 rounded-full bg-blue-100 text-blue-800 text-sm font-medium">
                            <BookOpen size={14} className="mr-1" /> {teacher.subject || 'Unknown'}
                          </div>
                        </div>
                      </div>

                      <div className="mt-6 grid grid-cols-2 gap-3">
                        <div
                          className="rounded-lg p-3"
                          style={{ backgroundColor: 'rgba(var(--color-teacher-rgb), 0.05)' }}>
                          <p className="text-xs text-gray-500 mb-1">Experience</p>
                          <p style={{ color: 'var(--color-teacher)' }} className="font-medium">
                            {teacher.experience || '5+ years'}
                          </p>
                        </div>
                        <div
                          className="rounded-lg p-3"
                          style={{ backgroundColor: 'rgba(var(--color-teacher-rgb), 0.05)' }}>
                          <p className="text-xs text-gray-500 mb-1">Rating</p>
                          <div className="flex items-center">
                            <Star size={14} className="text-yellow-500 mr-1" />
                            <span style={{ color: 'var(--color-teacher)' }} className="font-medium">
                              {teacher.rating || 4.5}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      className="px-6 py-3 border-t"
                      style={{
                        backgroundColor: 'rgba(var(--color-teacher-rgb), 0.03)',
                        borderColor: 'rgba(var(--color-teacher-rgb), 0.1)'
                      }}></div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Learning Resources - Interactive Grid */}
        {/* <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6 }}
          className="space-y-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900">
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Learning Resources
              </span>
            </h2>
            <p className="text-gray-600 mt-2 max-w-2xl mx-auto">
              Explore tools and materials to enhance your learning experience
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {dashboardWidgets.map((widget, index) => {
              const icons = [MessageSquare, BookOpen, Video, FileText, Rocket, Bot];
              const IconComponent = icons[index] || BookOpen;

              return (
                <motion.div
                  key={widget.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 * index + 0.6 }}
                  whileHover={{ y: -5 }}
                  className="relative group">
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-100 to-purple-100 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  <div className="relative h-full bg-white/80 backdrop-blur-sm rounded-2xl overflow-hidden border border-white/20 shadow-sm hover:shadow-md transition-all flex flex-col">
                    <div className="p-6 flex-1">
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">{widget.title}</h3>
                          <p className="text-sm text-gray-600 mt-1">{widget.description}</p>
                        </div>
                        <div className="p-3 rounded-lg bg-blue-50 text-blue-600">
                          <IconComponent size={20} />
                        </div>
                      </div>
                      <p className="text-2xl font-bold text-blue-600 mb-6">{widget.stat}</p>
                    </div>
                    <div className="px-6 pb-4">
                      <motion.button
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className="w-full py-2 px-4 rounded-lg font-medium text-white bg-[var(--color-counselor)] hover:from-blue-600 hover:to-purple-600 transition-all flex items-center justify-center gap-2">
                        Explore Now
                        <ChevronRight size={16} />
                      </motion.button>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </motion.div> */}
      </div>
    </div>
  );
};

export default StudentsDashboard;
