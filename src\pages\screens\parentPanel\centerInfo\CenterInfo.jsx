"use client"

import { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"
import { Building2, Mail, Phone, AlertCircle, Loader2, RefreshCw } from "lucide-react"
import { useLazyStudentDetailsServiceQuery, setstudentDetailsData } from "../studentsInfo/studentInfo.slice"

const CenterInfo = () => {
  const dispatch = useDispatch()
  
  const [triggerCenterDetails, { data,isLoading, isError, error }] = useLazyStudentDetailsServiceQuery()
  const center = data?.student
  useEffect(() => {
    const fetchCenterData = async () => {
      try {
        const response = await triggerCenterDetails().unwrap()
        console.log("API Response:", response) // Debug: Log API response
        dispatch(setstudentDetailsData(response))
        console.log("Dispatched to Redux:", response.center) // Debug: Log dispatched center data
      } catch (err) {
        console.error("Failed to fetch center details:", err)
      }
    }

    fetchCenterData()
  }, [triggerCenterDetails, dispatch])

  // Debug: Log center data when component renders
  console.log("Center Data in Component:", center)

  // Handle loading state
  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-gradient-to-br from-[#10e7dc] to-[#0bc5ba] rounded-2xl p-8 shadow-xl">
          <div className="bg-white/90 backdrop-blur-sm rounded-xl p-8">
            <div className="flex items-center justify-center space-x-4">
              <div className="relative">
                <Loader2 className="w-8 h-8 text-[#10e7dc] animate-spin" />
              </div>
              <div className="text-center">
                <p className="text-gray-700 font-semibold text-lg">Loading Center Information</p>
                <p className="text-gray-500 text-sm mt-1">Please wait while we fetch the details...</p>
                <button
                  onClick={() => triggerCenterDetails()}
                  className="mt-4 inline-flex items-center space-x-2 bg-[#10e7dc] hover:bg-[#0bc5ba] text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>Retry</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Handle error state
  if (isError) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-gradient-to-br from-red-500 to-red-600 rounded-2xl p-8 shadow-xl">
          <div className="bg-white/95 backdrop-blur-sm rounded-xl p-8 text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertCircle className="w-8 h-8 text-red-500" />
            </div>
            <h3 className="text-xl font-bold text-red-600 mb-2">Unable to Load Center Information</h3>
            <p className="text-red-500 mb-4">
              {error?.data?.message || error?.message || "An unexpected error occurred while fetching center details."}
            </p>
            <button
              onClick={() => triggerCenterDetails()}
              className="inline-flex items-center space-x-2 bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200"
            >
              <RefreshCw className="w-4 h-4" />
              <span>Retry</span>
            </button>
          </div>
        </div>
      </div>
    )
  }

  // Handle no center data
  if (!center) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-gradient-to-br from-gray-400 to-gray-500 rounded-2xl p-8 shadow-xl">
          <div className="bg-white/95 backdrop-blur-sm rounded-xl p-8 text-center">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Building2 className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-600 mb-2">No Center Information Available</h3>
            <p className="text-gray-500">Center details are not currently available. Please try again later.</p>
            <button
              onClick={() => triggerCenterDetails()}
              className="mt-4 inline-flex items-center space-x-2 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
            >
              <RefreshCw className="w-4 h-4" />
              <span>Retry</span>
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-5xl mx-auto p-6">
      <div className="relative overflow-hidden">
        {/* Main Container with Gradient Background */}
        <div className="relative rounded-3xl shadow-2xl overflow-hidden bg-gradient-to-br from-[#10e7dc] via-[#0bc5ba] to-[#08a69e]">
          {/* Animated Background Elements */}
          <div className="absolute inset-0">
            <div className="absolute top-0 left-0 w-64 h-64 bg-white/10 rounded-full -translate-x-32 -translate-y-32 animate-pulse"></div>
            <div
              className="absolute top-1/2 right-0 w-40 h-40 bg-white/10 rounded-full translate-x-20 animate-bounce"
              style={{ animationDuration: "3s" }}
            ></div>
            <div
              className="absolute bottom-0 left-1/3 w-24 h-24 bg-white/10 rounded-full translate-y-12 animate-pulse"
              style={{ animationDelay: "1s" }}
            ></div>
          </div>

          <div className="relative z-10 p-10">
            {/* Header Section */}
            <div className="text-center mb-5">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full mb-6">
                <Building2 className="w-10 h-10 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-white mb-3">Training Center</h1>
              <div className="w-32 h-1 bg-white/60 mx-auto rounded-full"></div>
              <p className="text-white/90 mt-4 text-lg">Comprehensive Center Information</p>
            </div>

            {/* Main Content Card */}
            <div className="bg-white/95 backdrop-blur-lg rounded-2xl p-8 shadow-xl">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Center Name Card */}
                <div className="group hover:scale-105 transition-all duration-300">
                  <div className="bg-gradient-to-br from-[#10e7dc] to-[#0bc5ba] rounded-xl p-6 text-white shadow-lg">
                    <div className="flex items-center justify-between mb-4">
                      <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center">
                        <Building2 className="w-6 h-6" />
                      </div>
                      <div className="w-8 h-8 bg-white/20 rounded-full"></div>
                    </div>
                    <h3 className="text-sm font-medium text-white/80 mb-2">Center Name</h3>
                    <p className="text-xl font-bold truncate" title={center?.center_name|| "N/A"}>
                      {center?.center_name || "Not Available"}
                    </p>
                  </div>
                </div>

                {/* Center Email Card */}
                <div className="group hover:scale-105 transition-all duration-300">
                  <div className="bg-gradient-to-br from-[#0bc5ba] to-[#08a69e] rounded-xl p-6 text-white shadow-lg">
                    <div className="flex items-center justify-between mb-4">
                      <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center">
                        <Mail className="w-6 h-6" />
                      </div>
                      <div className="w-8 h-8 bg-white/20 rounded-full"></div>
                    </div>
                    <h3 className="text-sm font-medium text-white/80 mb-2">Email Address</h3>
                    <p className="text-lg font-bold truncate" title={center?.center_email || "N/A"}>
                      {center?.center_email || "Not Available"}
                    </p>
                  </div>
                </div>

                {/* Center Phone Card */}
                <div className="group hover:scale-105 transition-all duration-300">
                  <div className="bg-gradient-to-br from-[#08a69e] to-[#067a73] rounded-xl p-6 text-white shadow-lg">
                    <div className="flex items-center justify-between mb-4">
                      <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center">
                        <Phone className="w-6 h-6" />
                      </div>
                      <div className="w-8 h-8 bg-white/20 rounded-full"></div>
                    </div>
                    <h3 className="text-sm font-medium text-white/80 mb-2">Phone Number</h3>
                    <p className="text-lg font-bold truncate" title={center?.center_phone || "N/A"}>
                      {center?.center_phone || "Not Available"}
                    </p>
                  </div>
                </div>
              </div>

              {/* Additional Info Section */}
              <div className="mt-8 p-6 bg-gradient-to-r from-[#10e7dc]/10 to-[#0bc5ba]/10 rounded-xl border-l-4 border-[#10e7dc]">
                <div className="flex items-start space-x-4">
                  <div className="w-10 h-10 bg-[#10e7dc]/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <AlertCircle className="w-5 h-5 text-[#10e7dc]" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800 mb-2">Center Information</h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      This training center provides comprehensive educational services and support. For any inquiries or
                      additional information, please use the contact details provided above.
                    </p>
                    <div className="mt-3">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-[#10e7dc]/20 text-[#10e7dc]">
                        Active Center
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CenterInfo