import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  User,
  Mail,
  Phone,
  BookOpen,
  MapPin,
  Users,
  Calendar,
  CheckCircle,
  XCircle,
  AlertCircle,
  TrendingUp,
  Target,
  Lightbulb,
  ChevronDown,
  ChevronUp,
  Award,
  BarChart2,
  Bookmark,
  Clock,
  Search,
  ThumbsUp,
  Star,
  HelpCircle,
  PieChart,
  Clipboard,
  Book,
  Shield,
  Rocket
} from 'lucide-react';
import { useGetParentStudentScoresQuery } from './overallPerformance.slice';
import { useSelector } from 'react-redux';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0 }
};

const cardVariants = {
  hover: {
    y: -5,
    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)'
  }
};

function OverallPerformance() {
  const { data, isLoading, error } = useGetParentStudentScoresQuery();
  const user = useSelector((state) => state.auth.user);
  const [testTypeFilter] = useState('all');
  const [expandedStudent, setExpandedStudent] = useState(null);
  const [expandedEvaluation, setExpandedEvaluation] = useState(null);

  if (isLoading) {
    return (
      <motion.div
        className="flex justify-center items-center h-screen"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}>
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ repeat: Infinity, duration: 1, ease: 'linear' }}
          className="rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"
        />
      </motion.div>
    );
  }

  if (error) {
    return (
      <motion.div
        className="flex justify-center items-center h-screen"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}>
        <motion.div
          className="bg-white rounded-xl shadow-lg p-8 w-96 text-center"
          initial={{ scale: 0.9 }}
          animate={{ scale: 1 }}>
          <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <motion.p
            className="text-red-700 font-medium text-lg"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}>
            {error.data?.message || 'Failed to load student scores'}
          </motion.p>
        </motion.div>
      </motion.div>
    );
  }

  if (!data?.students?.length) {
    return (
      <motion.div
        className="flex justify-center items-center h-screen"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}>
        <motion.div
          className="bg-white rounded-xl shadow-lg p-8 w-96 text-center"
          initial={{ scale: 0.9 }}
          animate={{ scale: 1 }}>
          <AlertCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <motion.p
            className="text-gray-600 font-medium text-lg"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}>
            No evaluations found for your children
          </motion.p>
        </motion.div>
      </motion.div>
    );
  }

  const filteredStudents = data.students.map((student) => ({
    ...student,
    evaluations:
      testTypeFilter === 'all'
        ? student.evaluations
        : student.evaluations.filter(
            (evaluation) => evaluation.test_type?.toLowerCase() === testTypeFilter
          )
  }));

  const getScoreColor = (marks, total = 2) => {
    const percentage = (marks / total) * 100;
    if (percentage >= 80) return 'text-green-700 bg-green-100 border-green-200';
    if (percentage >= 60) return 'text-yellow-700 bg-yellow-100 border-yellow-200';
    return 'text-red-700 bg-red-100 border-red-200';
  };

  const toggleStudent = (studentId) => {
    setExpandedStudent(expandedStudent === studentId ? null : studentId);
    setExpandedEvaluation(null);
  };

  const toggleEvaluation = (testId) => {
    setExpandedEvaluation(expandedEvaluation === testId ? null : testId);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 py-8 px-4">
      <div className="container mx-auto max-w-7xl">
        {/* Animated Header */}
        <motion.div
          className="mb-12 text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}>
        <motion.div 
  className="relative mb-8 group"
  initial={{ opacity: 0, y: -20 }}
  animate={{ opacity: 1, y: 0 }}
  transition={{ duration: 0.5 }}
  whileHover={{ scale: 1.02 }}
>
  {/* Decorative elements */}
  <div className="absolute -left-4 -top-4 h-16 w-16 rounded-full bg-[var(--color-parents)]/10 blur-md group-hover:bg-[var(--color-parents)]/20 transition-all"></div>
  <div className="absolute -right-4 -bottom-4 h-12 w-12 rounded-full bg-[var(--color-parents)]/10 blur-md group-hover:bg-[var(--color-parents)]/20 transition-all"></div>
  
  {/* Main header */}
  <div className="relative z-10 bg-gradient-to-r from-[var(--color-parents)]/5 to-[var(--color-parents)]/10 backdrop-blur-sm border border-[var(--color-teacher)]/20 rounded-2xl px-8 py-6 shadow-lg overflow-hidden">
    {/* Subtle pattern overlay */}
    <div className="absolute inset-0 opacity-5 [mask-image:linear-gradient(to_bottom,transparent,white)]">
      <svg className="absolute inset-0 h-full w-full" xmlns="http://www.w3.org/2000/svg">
        <pattern id="pattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
          <path d="M0 40L40 0H20L0 20M40 40V20L20 40" fill="none" stroke="currentColor" strokeWidth="1.5" />
        </pattern>
        <rect width="100%" height="100%" fill="url(#pattern)" />
      </svg>
    </div>
    
    {/* Content */}
    <div className="relative z-20 flex items-center space-x-4">
      <div className="p-3 rounded-xl bg-[var(--color-parents)] text-white shadow-lg">
        <BookOpen className="h-8 w-8" />
      </div>
      <div>
        <h1 className="text-3xl font-bold text-[var(--color-teacher)]">Paper Based Test Result</h1>
        <p className="text-sm text-gray-500 mt-1">Review and analyze student performance metrics</p>
      </div>
    </div>
    
    {/* Animated underline */}
    <motion.div 
      className="absolute bottom-0 left-0 h-1 bg-[var(--color-counselor)]"
      initial={{ width: 0 }}
      animate={{ width: '100%' }}
      transition={{ duration: 1, delay: 0.3 }}
    />
  </div>
</motion.div>
          <motion.p
            className="text-gray-600 text-lg max-w-2xl mx-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}>
            Track and analyze your children's academic journey with interactive insights
          </motion.p>
        </motion.div>

        {/* Summary Cards */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12"
          variants={containerVariants}
          initial="hidden"
          animate="show">
          <motion.div
            className="bg-white rounded-2xl shadow-md p-6 border-l-4 border-[var(--color-teacher)]"
            variants={itemVariants}
            whileHover={{ scale: 1.03 }}>
            <div className="flex items-center">
              <div className="bg-blue-100 p-3 rounded-full mr-4">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h3 className="text-gray-500 text-sm font-medium">Children</h3>
                <p className="text-2xl font-bold text-gray-800">{data.students.length}</p>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="bg-white rounded-2xl shadow-md p-6 border-l-4 border-green-500"
            variants={itemVariants}
            whileHover={{ scale: 1.03 }}>
            <div className="flex items-center">
              <div className="bg-green-100 p-3 rounded-full mr-4">
                <BookOpen className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <h3 className="text-gray-500 text-sm font-medium">Total Evaluations</h3>
                <p className="text-2xl font-bold text-gray-800">
                  {data.students.reduce((acc, student) => acc + student.evaluations.length, 0)}
                </p>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="bg-white rounded-2xl shadow-md p-6 border-l-4 border-[var(--color-teacher)]"
            variants={itemVariants}
            whileHover={{ scale: 1.03 }}>
            <div className="flex items-center">
              <div className="bg-purple-100 p-3 rounded-full mr-4">
                <Award className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <h3 className="text-gray-500 text-sm font-medium">Average Score</h3>
                <p className="text-2xl font-bold text-gray-800">
                  {(
                    (data.students.reduce((acc, student) => {
                      const studentTotal = student.evaluations.reduce((sum, evaluation) => {
                        return (
                          sum +
                          evaluation.performance_analysis.reduce(
                            (s, item) => s + item.marks_awarded,
                            0
                          )
                        );
                      }, 0);
                      return acc + studentTotal;
                    }, 0) /
                      data.students.reduce((acc, student) => {
                        return (
                          acc +
                          student.evaluations.reduce(
                            (sum, evaluation) => sum + evaluation.performance_analysis.length * 2,
                            0
                          )
                        );
                      }, 0)) *
                      100 || 0
                  ).toFixed(1)}
                  %
                </p>
              </div>
            </div>
          </motion.div>
        </motion.div>

        {/* Students Accordion */}
        <motion.div
          className="space-y-6"
          variants={containerVariants}
          initial="hidden"
          animate="show">
          {filteredStudents.map((student) => (
            <motion.div
              key={student.student_id}
              className="bg-white rounded-2xl shadow-lg overflow-hidden"
              variants={itemVariants}
              layout>
              <motion.div
                className="p-6 cursor-pointer"
                onClick={() => toggleStudent(student.student_id)}
                whileHover={{ backgroundColor: 'rgba(239, 246, 255, 0.5)' }}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="relative">
                      <div className="bg-blue-100 rounded-full p-3">
                        <User className="h-6 w-6 text-blue-600" />
                      </div>
                      {student.evaluations.length > 0 && (
                        <motion.div
                          className="absolute -top-2 -right-2 bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold"
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}>
                          {student.evaluations.length}
                        </motion.div>
                      )}
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-gray-900">{student.first_name}</h2>
                      <div className="flex items-center space-x-2 text-sm text-gray-600">
                        <span className="flex items-center">
                          <BookOpen className="h-4 w-4 mr-1" />
                          {student.course}
                        </span>
                        <span className="flex items-center">
                          <MapPin className="h-4 w-4 mr-1" />
                          {student.center_code}
                        </span>
                      </div>
                    </div>
                  </div>
                  {expandedStudent === student.student_id ? (
                    <ChevronUp className="h-6 w-6 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-6 w-6 text-gray-500" />
                  )}
                </div>
              </motion.div>

              <AnimatePresence>
                {expandedStudent === student.student_id && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.3 }}
                    className="px-6 pb-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <div className="bg-blue-50 rounded-xl p-4">
                        <h3 className="font-medium text-blue-800 mb-2 flex items-center">
                          <Mail className="h-5 w-5 mr-2" />
                          Contact Information
                        </h3>
                        <p className="text-gray-700">{student.student_email}</p>
                        <p className="text-gray-700">{student.phone}</p>
                      </div>
                      <div className="bg-green-50 rounded-xl p-4">
                        <h3 className="font-medium text-green-800 mb-2 flex items-center">
                          <Users className="h-5 w-5 mr-2" />
                          Batch Information
                        </h3>
                        <p className="text-gray-700">
                          {student.batch.batch_name || student.batch.message}
                        </p>
                      </div>
                    </div>

                    <div className="mb-6">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                        <TrendingUp className="h-6 w-6 text-blue-600 mr-2" />
                        Performance Overview
                      </h3>

                      {student.evaluations.length > 0 ? (
                        <div className="space-y-4">
                          {student.evaluations.map((evaluation) => (
                            <motion.div
                              key={evaluation.test_id}
                              className="border border-gray-200 rounded-xl overflow-hidden"
                              variants={cardVariants}
                              whileHover="hover"
                              layout>
                              <div
                                className="bg-gray-50 p-4 cursor-pointer"
                                onClick={() => toggleEvaluation(evaluation.test_id)}>
                                <div className="flex justify-between items-center">
                                  <div className="flex items-center space-x-3">
                                    <div className="bg-purple-100 p-2 rounded-lg">
                                      <Clipboard className="h-5 w-5 text-purple-600" />
                                    </div>
                                    <div>
                                      <h4 className="font-medium text-gray-900">
                                        {evaluation.test_type || 'Evaluation'} -{' '}
                                        {new Date(
                                          evaluation.overall_feedback.evaluation_timestamp
                                        ).toLocaleDateString()}
                                      </h4>
                                      <p className="text-sm text-gray-600">
                                        Faculty: {evaluation.faculty_id} • Questions:{' '}
                                        {evaluation.performance_analysis.length}
                                      </p>
                                    </div>
                                  </div>
                                  {expandedEvaluation === evaluation.test_id ? (
                                    <ChevronUp className="h-5 w-5 text-gray-500" />
                                  ) : (
                                    <ChevronDown className="h-5 w-5 text-gray-500" />
                                  )}
                                </div>
                              </div>

                              <AnimatePresence>
                                {expandedEvaluation === evaluation.test_id && (
                                  <motion.div
                                    initial={{ opacity: 0, height: 0 }}
                                    animate={{ opacity: 1, height: 'auto' }}
                                    exit={{ opacity: 0, height: 0 }}
                                    transition={{ duration: 0.3 }}
                                    className="p-6">
                                    {/* Performance Summary */}
                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                                      <div className="bg-blue-50 rounded-lg p-4">
                                        <div className="flex items-center mb-2">
                                          <Star className="h-5 w-5 text-yellow-500 mr-2" />
                                          <h5 className="font-medium text-gray-700">Total Score</h5>
                                        </div>
                                        <p className="text-2xl font-bold text-gray-900">
                                          {evaluation.performance_analysis.reduce(
                                            (sum, item) => sum + item.marks_awarded,
                                            0
                                          )}
                                          /{evaluation.performance_analysis.length * 2}
                                        </p>
                                      </div>
                                      <div className="bg-green-50 rounded-lg p-4">
                                        <div className="flex items-center mb-2">
                                          <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                                          <h5 className="font-medium text-gray-700">
                                            Correct Answers
                                          </h5>
                                        </div>
                                        <p className="text-2xl font-bold text-gray-900">
                                          {
                                            evaluation.performance_analysis.filter(
                                              (item) => item.error_type === 'NO_MISTAKES'
                                            ).length
                                          }
                                        </p>
                                      </div>
                                      <div className="bg-red-50 rounded-lg p-4">
                                        <div className="flex items-center mb-2">
                                          <XCircle className="h-5 w-5 text-red-500 mr-2" />
                                          <h5 className="font-medium text-gray-700">
                                            Areas to Improve
                                          </h5>
                                        </div>
                                        <p className="text-2xl font-bold text-gray-900">
                                          {
                                            evaluation.performance_analysis.filter(
                                              (item) => item.error_type !== 'NO_MISTAKES'
                                            ).length
                                          }
                                        </p>
                                      </div>
                                    </div>

                                    {/* Question Analysis */}
                                    <div className="mb-6">
                                      <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                        <BarChart2 className="h-5 w-5 text-blue-600 mr-2" />
                                        Detailed Question Analysis
                                      </h4>

                                      <div className="space-y-6">
                                        {evaluation.performance_analysis.map((item, idx) => (
                                          <motion.div
                                            key={idx}
                                            className="border border-gray-200 rounded-xl overflow-hidden shadow-sm"
                                            initial={{ opacity: 0, y: 10 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            transition={{ delay: idx * 0.05 }}
                                            whileHover={{ scale: 1.02 }}>
                                            {/* Question Header */}
                                            <div
                                              className={`p-4 ${item.error_type === 'NO_MISTAKES' ? 'bg-green-50' : 'bg-red-50'} flex justify-between items-center`}>
                                              <div className="flex items-center space-x-3">
                                                <div
                                                  className={`p-2 rounded-lg ${item.error_type === 'NO_MISTAKES' ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'}`}>
                                                  {item.error_type === 'NO_MISTAKES' ? (
                                                    <CheckCircle className="h-5 w-5" />
                                                  ) : (
                                                    <XCircle className="h-5 w-5" />
                                                  )}
                                                </div>
                                                <div>
                                                  <h3 className="font-medium text-gray-900">
                                                    Question {idx + 1}
                                                  </h3>
                                                  <div className="flex items-center space-x-2">
                                                    <span
                                                      className={`px-2 py-0.5 rounded-full text-xs font-bold ${getScoreColor(item.marks_awarded)}`}>
                                                      {item.marks_awarded}/2 points
                                                    </span>
                                                    <span className="text-xs text-gray-500">
                                                      {item.error_type === 'NO_MISTAKES'
                                                        ? 'Perfect answer'
                                                        : item.error_type.replace(/_/g, ' ')}
                                                    </span>
                                                  </div>
                                                </div>
                                              </div>
                                              <div className="text-sm font-medium text-gray-500">
                                                Concept:{' '}
                                                <span className="text-gray-700">
                                                  {item.feedback.core_concept_tested}
                                                </span>
                                              </div>
                                            </div>

                                            {/* Answer Comparison */}
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-6">
                                              <div className="space-y-2">
                                                <div className="flex items-center space-x-2 text-sm font-medium text-gray-500">
                                                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                                                  <span>Correct Answer</span>
                                                </div>
                                                <div className="bg-green-50 border border-green-100 rounded-lg p-4">
                                                  <p className="font-mono text-green-800 font-medium">
                                                    {item.expert_calculated_answer}
                                                  </p>
                                                </div>
                                              </div>

                                              <div className="space-y-2">
                                                <div className="flex items-center space-x-2 text-sm font-medium text-gray-500">
                                                  <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                                                  <span>Student's Answer</span>
                                                </div>
                                                <div className="bg-blue-50 border border-blue-100 rounded-lg p-4">
                                                  <p className="font-mono text-blue-800 font-medium">
                                                    {item.student_final_answer}
                                                  </p>
                                                </div>
                                              </div>
                                            </div>

                                            {/* Feedback Sections */}
                                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 p-6 pt-0">
                                              {/* Analysis */}
                                              <div className="bg-gray-50 rounded-lg p-4">
                                                <div className="flex items-center space-x-2 mb-2">
                                                  <Search className="h-4 w-4 text-gray-600" />
                                                  <h4 className="font-medium text-gray-700">
                                                    Analysis
                                                  </h4>
                                                </div>
                                                <p className="text-sm text-gray-600">
                                                  {item.feedback.error_analysis}
                                                </p>
                                              </div>

                                              {/* Positive Feedback */}
                                              <div className="bg-green-50 rounded-lg p-4">
                                                <div className="flex items-center space-x-2 mb-2">
                                                  <ThumbsUp className="h-4 w-4 text-green-600" />
                                                  <h4 className="font-medium text-green-700">
                                                    Positive Feedback
                                                  </h4>
                                                </div>
                                                <p className="text-sm text-green-600">
                                                  {item.feedback.positive_feedback}
                                                </p>
                                              </div>

                                              {/* Suggestions */}
                                              <div className="bg-blue-50 rounded-lg p-4">
                                                <div className="flex items-center space-x-2 mb-2">
                                                  <Lightbulb className="h-4 w-4 text-blue-600" />
                                                  <h4 className="font-medium text-blue-700">
                                                    Improvement Suggestion
                                                  </h4>
                                                </div>
                                                <p className="text-sm text-blue-600">
                                                  {item.feedback.improvement_suggestion}
                                                </p>
                                              </div>
                                            </div>
                                          </motion.div>
                                        ))}
                                      </div>
                                    </div>

                                    {/* Overall Feedback */}
                                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6">
                                      <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                        <Lightbulb className="h-5 w-5 text-yellow-600 mr-2" />
                                        Teacher's Feedback
                                      </h4>

                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                          <h5 className="font-medium text-blue-700 mb-2 flex items-center">
                                            <Shield className="h-4 w-4 mr-2" />
                                            Strengths
                                          </h5>
                                          <ul className="space-y-2">
                                            {evaluation.overall_feedback.conceptual_strengths?.map(
                                              (strength, idx) => (
                                                <motion.li
                                                  key={idx}
                                                  className="flex items-start"
                                                  initial={{ opacity: 0, x: -10 }}
                                                  animate={{ opacity: 1, x: 0 }}
                                                  transition={{ delay: idx * 0.1 }}>
                                                  <span className="bg-blue-100 text-blue-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mt-0.5 mr-2">
                                                    ✓
                                                  </span>
                                                  <span className="text-gray-700">{strength}</span>
                                                </motion.li>
                                              )
                                            )}
                                          </ul>
                                        </div>
                                        <div>
                                          <h5 className="font-medium text-orange-700 mb-2 flex items-center">
                                            <HelpCircle className="h-4 w-4 mr-2" />
                                            Improvement Areas
                                          </h5>
                                          <p className="text-gray-700 mb-4">
                                            {evaluation.overall_feedback
                                              .primary_area_for_improvement ||
                                              'General concept understanding'}
                                          </p>

                                          <h5 className="font-medium text-purple-700 mb-2 flex items-center">
                                            <Book className="h-4 w-4 mr-2" />
                                            Action Plan
                                          </h5>
                                          <ul className="space-y-2">
                                            {evaluation.overall_feedback.strategic_action_plan?.map(
                                              (plan, idx) => (
                                                <motion.li
                                                  key={idx}
                                                  className="flex items-start"
                                                  initial={{ opacity: 0, x: -10 }}
                                                  animate={{ opacity: 1, x: 0 }}
                                                  transition={{ delay: idx * 0.1 }}>
                                                  <span className="bg-purple-100 text-purple-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mt-0.5 mr-2">
                                                    {idx + 1}
                                                  </span>
                                                  <span className="text-gray-700">
                                                    {plan.action || 'Review key concepts'}
                                                  </span>
                                                </motion.li>
                                              )
                                            )}
                                          </ul>
                                        </div>
                                      </div>
                                    </div>
                                  </motion.div>
                                )}
                              </AnimatePresence>
                            </motion.div>
                          ))}
                        </div>
                      ) : (
                        <motion.div
                          className="border-dashed border-2 border-gray-200 rounded-xl p-8 text-center"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}>
                          <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                          <p className="text-gray-600 font-medium">
                            No evaluations available for {student.first_name}
                          </p>
                        </motion.div>
                      )}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </div>
  );
}

export default OverallPerformance;
