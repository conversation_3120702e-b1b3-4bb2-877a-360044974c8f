# --- START OF FULLY UPDATED FILE: agentic_translation_workflow.py ---

import asyncio
import logging
from typing import Dict, Callable

from translation_service import TranslationService

logger = logging.getLogger(__name__)

class IntelligentSegmenter:
    """Waits for a complete thought before sending text for translation."""
    def __init__(self, on_segment_ready: Callable):
        self.segments: Dict[str, str] = {}
        self.timers: Dict[str, asyncio.Task] = {}
        self.segment_timeout = 2.5
        self.on_segment_ready_callback = on_segment_ready

    async def _commit_segment(self, session_id: str):
        if self.segments.get(session_id):
            segment_to_translate = self.segments[session_id].strip()
            self.segments[session_id] = ""
            if segment_to_translate:
                logger.info(f"🧠 Segmenter committed thought: '{segment_to_translate}'")
                await self.on_segment_ready_callback(session_id, segment_to_translate)

    async def add_text(self, session_id: str, text: str):
        if session_id not in self.segments: self.segments[session_id] = ""
        if session_id in self.timers: self.timers[session_id].cancel()
            
        self.segments[session_id] += f" {text}"

        if text.strip().endswith(('.', '?', '!')):
            await self._commit_segment(session_id)
        else:
            self.timers[session_id] = asyncio.create_task(self._delayed_commit(session_id))

    async def _delayed_commit(self, session_id: str):
        await asyncio.sleep(self.segment_timeout)
        await self._commit_segment(session_id)

    def cleanup(self, session_id: str):
        if session_id in self.timers: self.timers[session_id].cancel()
        if session_id in self.segments: del self.segments[session_id]

class AgenticTranslationWorkflow:
    def __init__(self, translation_service: TranslationService):
        self.translation_service = translation_service
        self.sessions: Dict[str, Dict] = {}
        self.result_callbacks: Dict[str, Callable] = {}
        self.processing_queues: Dict[str, asyncio.Queue] = {}
        self.worker_tasks: Dict[str, asyncio.Task] = {}
        self.segmenter = IntelligentSegmenter(on_segment_ready=self.queue_translation_job)

    async def initialize_session(self, session_id: str, source_lang: str, target_lang: str):
        logger.info(f"🤖 Initializing text-based workflow for session {session_id} ({source_lang}->{target_lang})")
        self.sessions[session_id] = {"source_lang": source_lang, "target_lang": target_lang}
        self.processing_queues[session_id] = asyncio.Queue()
        self.worker_tasks[session_id] = asyncio.create_task(self._session_worker(session_id))
        
    async def process_stt_result(self, session_id: str, text: str):
        """Receives text from STT and passes it to the segmenter."""
        if text:
            await self.segmenter.add_text(session_id, text)

    async def process_direct_text(self, session_id: str, text: str):
        """Process direct text input from teacher's speech recognition."""
        if session_id not in self.sessions:
            logger.warning(f"Session {session_id} not found for direct text processing")
            return

        logger.info(f"📝 Processing direct text from teacher for session {session_id}: '{text}'")
        # Skip segmentation for direct text and process immediately
        await self.queue_translation_job(session_id, text)

    async def queue_translation_job(self, session_id: str, text_to_translate: str):
        """Adds a complete sentence to the processing queue."""
        if session_id in self.processing_queues:
            await self.processing_queues[session_id].put(text_to_translate)

    async def _session_worker(self, session_id: str):
        """The background worker that processes the translation queue."""
        while session_id in self.sessions:
            try:
                original_text = await self.processing_queues[session_id].get()
                session_info = self.sessions[session_id]
                
                translation_result = await self.translation_service.translate_text(
                    text=original_text, 
                    source_lang=session_info["source_lang"], 
                    target_lang=session_info["target_lang"]
                )
                if not translation_result:
                    self.processing_queues[session_id].task_done()
                    continue

                audio_b64 = await self.translation_service.text_to_speech(
                    translation_result["ttsText"], session_info["target_lang"]
                )

                if session_id in self.result_callbacks and audio_b64:
                    await self.result_callbacks[session_id]({
                        "original_text": original_text,
                        "translated_text": translation_result["displayText"],
                        "audio_data": audio_b64
                    })
                        
                self.processing_queues[session_id].task_done()
            except asyncio.CancelledError:
                logger.info(f"Worker for session {session_id} is shutting down.")
                break
            except Exception as e:
                logger.error(f"Error in session worker for {session_id}: {e}", exc_info=True)
                if 'job' in locals(): self.processing_queues[session_id].task_done()

    async def cleanup_session(self, session_id: str):
        if session_id in self.worker_tasks:
            self.worker_tasks[session_id].cancel()
        
        self.segmenter.cleanup(session_id)
        
        for res_dict in [self.worker_tasks, self.sessions, self.processing_queues, self.result_callbacks]:
            if session_id in res_dict: del res_dict[session_id]
        
        logger.info(f"🧹 Cleaned up text-based workflow resources for session {session_id}")