# --- START OF FULLY UPDATED FILE: app.py ---

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import asyncio
import logging
import os
from typing import Dict, List
import uuid
from dotenv import load_dotenv

load_dotenv()

from translation_service import TranslationService
from websocket_handler import WebSocketManager
from agentic_translation_workflow import AgenticTranslationWorkflow
from audio_processor import AudioProcessor

# --- Configuration ---
ENVIRONMENT = os.getenv("ENVIRONMENT", "development")
HOST = os.getenv("HOST", "0.0.0.0")
PORT = int(os.getenv("PORT", "8022"))

def get_allowed_origins():
    origins_env = os.getenv("ALLOWED_ORIGINS", "")
    default_origins = ["http://localhost:3000", "http://localhost:5173", "null"]
    if origins_env:
        custom_origins = [origin.strip() for origin in origins_env.split(",") if origin.strip()]
        return list(set(default_origins + custom_origins))
    return default_origins

ALLOWED_ORIGINS = get_allowed_origins()
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")

logging.basicConfig(level=getattr(logging, LOG_LEVEL.upper(), logging.INFO), format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Component Initialization ---
translation_service = TranslationService()
websocket_manager = WebSocketManager()
agentic_workflow = AgenticTranslationWorkflow(translation_service)
audio_processor = AudioProcessor()
active_sessions: Dict[str, dict] = {}

@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("Starting Live Translation Service (OpenAI-Only Translation)...")
    await translation_service.initialize()
    logger.info("Translation Service initialized successfully")
    yield
    logger.info("Shutting down Live Translation Service...")
    await websocket_manager.disconnect_all()
    logger.info("Translation Service shutdown complete")

app = FastAPI(
    title="Live Translation Service",
    version="11.0.0-legend-tier",
    lifespan=lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=ALLOWED_ORIGINS, allow_credentials=True,
    allow_methods=["*"], allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"service": "Live Translation Service", "status": "running", "version": "11.0.0-legend-tier"}

@app.post("/api/translate/start-session")
async def start_translation_session(request_data: dict, request: Request):
    session_id = str(uuid.uuid4())
    source_lang = request_data.get("source_language", "en")
    target_lang = request_data.get("target_language", "ta")

    session_data = {
        "session_id": session_id, "source_language": source_lang, "target_language": target_lang,
        "transcription_history": []
    }
    active_sessions[session_id] = session_data

    await agentic_workflow.initialize_session(session_id, source_lang, target_lang)
    audio_processor.initialize_session(session_id)

    host = request.headers.get("host", f"localhost:{PORT}")
    scheme = "wss" if "sasthra.in" in host or ENVIRONMENT == "production" else "ws"
    websocket_url = f"{scheme}://{host}/ws/translate/{session_id}"

    logger.info(f"Session {session_id} created for {source_lang}->{target_lang}. WebSocket URL: {websocket_url}")
    return {"success": True, "session_id": session_id, "websocket_url": websocket_url}

@app.post("/api/translate/stop-session")
async def stop_translation_session(request_data: dict):
    session_id = request_data.get("session_id")
    if not session_id or session_id not in active_sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    logger.info(f"Received request to stop session {session_id}")
    await agentic_workflow.cleanup_session(session_id)
    audio_processor.cleanup_session(session_id)
    if session_id in active_sessions:
        websocket = active_sessions[session_id].get("websocket")
        if websocket: await websocket_manager.disconnect(websocket)
        del active_sessions[session_id]
    logger.info(f"Successfully stopped and cleaned up session {session_id}")
    return {"success": True, "message": f"Session {session_id} stopped."}

@app.websocket("/ws/translate/{session_id}")
async def websocket_translate_endpoint(websocket: WebSocket, session_id: str):
    if session_id not in active_sessions:
        await websocket.close(code=4004, reason="Session not found")
        return
    await websocket_manager.connect(websocket, session_id)
    active_sessions[session_id]["websocket"] = websocket

    async def websocket_result_callback(result: Dict):
        try:
            logger.info(f"📡 WebSocket callback triggered for session {session_id}")
            logger.info(f"📡 WebSocket state: {websocket.client_state.name}")
            logger.info(f"📡 Result data keys: {list(result.keys())}")

            if websocket.client_state.name != 'CONNECTED':
                logger.warning(f"📡 WebSocket for session {session_id} not connected, state: {websocket.client_state.name}")
                return

            # Send translation text
            text_payload = {"type": "translation", "original_text": result.get("original_text"), "translated_text": result.get("translated_text")}
            logger.info(f"📡 Sending translation text to frontend...")
            await websocket.send_json(text_payload)
            logger.info(f"📡 Translation text sent successfully")

            # Send audio if available
            if result.get("audio_data"):
                audio_data_size = len(result["audio_data"])
                logger.info(f"📡 Sending audio data to frontend - Size: {audio_data_size} characters")
                audio_payload = {"type": "translated_audio", "audio_data": result["audio_data"]}
                await websocket.send_json(audio_payload)
                logger.info(f"📡 Audio data sent successfully to frontend")
            else:
                logger.warning(f"📡 No audio data in result to send to frontend")

        except Exception as e:
            logger.error(f"📡 Error in WebSocket result callback for {session_id}: {e}", exc_info=True)

    agentic_workflow.result_callbacks[session_id] = websocket_result_callback
    logger.info(f"📡 Registered WebSocket result callback for session {session_id}")

    try:
        logger.info(f"WebSocket connected for session {session_id}")
        await websocket.send_json({"type": "connection_established", "session_id": session_id})

        session_info = active_sessions[session_id]
        source_lang = session_info["source_language"]

        whisper_context = "This is a science lecture for NEET and JEE. Common terms are combustion, exothermic, mitochondria, velocity, thermodynamics, kinetic energy, potential energy."

        while True:
            try:
                # Try to receive as JSON first (text message from teacher)
                message_data = await websocket.receive_json()
                logger.debug(f"Received JSON message for session {session_id}: {message_data.get('type', 'unknown')}")
                if message_data.get("type") == "transcript":
                    # Direct text input from teacher's speech recognition
                    text_result = message_data.get("text", "").strip()
                    if text_result:
                        logger.info(f"Received direct transcript from teacher: {text_result}")
                        history = session_info["transcription_history"]
                        history.append(text_result)
                        if len(history) > 5:
                            session_info["transcription_history"] = history[-5:]

                        # Use direct text processing for teacher input (skips segmentation)
                        await agentic_workflow.process_direct_text(session_id, text_result)
                    continue
            except:
                # If JSON parsing fails, try to receive as bytes (audio data from viewer)
                try:
                    message_bytes = await websocket.receive_bytes()
                    logger.debug(f"Received binary audio data for session {session_id}: {len(message_bytes)} bytes")
                    speech_audio = await audio_processor.process_audio_chunk(session_id, message_bytes)

                    if speech_audio:
                        history = session_info["transcription_history"]
                        initial_prompt_context = f"{whisper_context}\n\n" + "\n".join(history)

                        text_result = await translation_service.speech_to_text(
                            speech_audio, language=source_lang, prompt=initial_prompt_context
                        )

                        if text_result:
                            history.append(text_result)
                            if len(history) > 5:
                                session_info["transcription_history"] = history[-5:]

                            await agentic_workflow.process_stt_result(session_id, text_result)
                except Exception as e:
                    logger.error(f"Error processing WebSocket message: {e}")
                    break

    except WebSocketDisconnect:
        logger.info(f"Client for session {session_id} disconnected.")
    finally:
        logger.info(f"Cleaning up resources for session {session_id} in 'finally' block.")
        await websocket_manager.disconnect(websocket)
        await agentic_workflow.cleanup_session(session_id)
        audio_processor.cleanup_session(session_id)
        if session_id in active_sessions: del active_sessions[session_id]

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app:app", host=HOST, port=PORT, log_level=LOG_LEVEL.lower(), reload=True)

