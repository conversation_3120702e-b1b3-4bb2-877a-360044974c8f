
import React, { useState } from 'react';
import data from '../boosterModule/data.json';
import { Pie } from 'react-chartjs-2';
import { BarChart3, Table, TrendingUp, BookOpen, Award, ChevronDown } from 'lucide-react';
import {
  Chart as ChartJS,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels';

ChartJS.register(
  ArcElement,
  Title,
  Tooltip,
  Legend,
  ChartDataLabels
);

const BoosterModule = () => {
  const [selectedSubject, setSelectedSubject] = useState('Physics');
  const [selectedYear, setSelectedYear] = useState('2024');
  const [viewType, setViewType] = useState('chart'); // 'chart' or 'table'

  const subjects = Object.keys(data);
  const years = ['2024', '2023', '2022', '2021'];

  const handleSubjectChange = (e) => {
    setSelectedSubject(e.target.value);
  };

  const handleYearChange = (e) => {
    setSelectedYear(e.target.value);
  };

  const toggleView = () => {
    setViewType(viewType === 'chart' ? 'table' : 'chart');
  };

  const chapters = data[selectedSubject] || [];

  // Calculate total questions for percentage calculation
  const totalQuestions = chapters.reduce((sum, chapter) => {
    const questions = chapter.NEET[selectedYear];
    return sum + (questions !== null && questions !== undefined ? questions : 0);
  }, 0);

  // Generate colors for pie chart
  const generateColors = (count) => {
    const colors = [
      'rgba(79, 172, 254, 0.8)',
      'rgba(255, 107, 107, 0.8)',
      'rgba(72, 207, 173, 0.8)',
      'rgba(255, 182, 72, 0.8)',
      'rgba(162, 155, 254, 0.8)',
      'rgba(255, 159, 243, 0.8)',
      'rgba(34, 197, 94, 0.8)',
      'rgba(239, 68, 68, 0.8)',
      'rgba(168, 85, 247, 0.8)',
      'rgba(245, 158, 11, 0.8)',
      'rgba(20, 184, 166, 0.8)',
      'rgba(236, 72, 153, 0.8)',
      'rgba(99, 102, 241, 0.8)',
      'rgba(248, 113, 113, 0.8)',
      'rgba(52, 211, 153, 0.8)',
      'rgba(251, 191, 36, 0.8)',
      'rgba(139, 92, 246, 0.8)',
      'rgba(156, 163, 175, 0.8)',
      'rgba(14, 165, 233, 0.8)',
      'rgba(34, 197, 94, 0.8)',
    ];
    
    // If we need more colors, generate additional ones
    const additionalColors = [];
    for (let i = colors.length; i < count; i++) {
      const hue = (i * 137.508) % 360; // Golden angle approximation
      additionalColors.push(`hsla(${hue}, 70%, 60%, 0.8)`);
    }
    
    return [...colors, ...additionalColors].slice(0, count);
  };

  const pieChartData = {
    labels: chapters.map(chapter => chapter.Chapter_name),
    datasets: [
      {
        label: `Questions in ${selectedYear}`,
        data: chapters.map(chapter => 
          chapter.NEET[selectedYear] !== null && chapter.NEET[selectedYear] !== undefined 
            ? chapter.NEET[selectedYear] 
            : 0
        ),
        backgroundColor: generateColors(chapters.length),
        borderColor: generateColors(chapters.length).map(color => color.replace('0.8', '1')),
        borderWidth: 3,
      },
    ],
  };

  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false, // We'll create a custom legend
      },
      title: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const value = context.parsed;
            const percentage = totalQuestions > 0 ? ((value / totalQuestions) * 100).toFixed(1) : 0;
            return `${context.label}: ${value} questions (${percentage}%)`;
          }
        },
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
        cornerRadius: 8,
      },
      datalabels: {
        display: true,
        color: 'white',
        font: {
          weight: 'bold',
          size: 11,
        },
        formatter: function(value, context) {
          if (value === 0) return '';
          const percentage = totalQuestions > 0 ? ((value / totalQuestions) * 100).toFixed(1) : 0;
          return percentage >= 5 ? percentage + '%' : '';
        },
        anchor: 'center',
        align: 'center',
      }
    },
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center">
                <BookOpen className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">NEET Analysis Dashboard</h1>
                <p className="text-sm text-gray-600">Comprehensive chapter-wise question analysis</p>
              </div>
            </div>
            
            {/* View Toggle */}
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewType('chart')}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-all ${
                  viewType === 'chart'
                    ? 'bg-white text-purple-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <BarChart3 className="w-4 h-4" />
                <span>Chart View</span>
              </button>
              <button
                onClick={() => setViewType('table')}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-all ${
                  viewType === 'table'
                    ? 'bg-white text-purple-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Table className="w-4 h-4" />
                <span>Table View</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Controls */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 mb-8">
          <div className="flex flex-wrap items-center gap-6">
            <div className="flex items-center space-x-3">
              <Award className="w-5 h-5 text-purple-500" />
              <span className="text-sm font-medium text-gray-700">Filters</span>
            </div>
            
            <div className="flex flex-wrap gap-4">
              <div className="relative">
                <label className="block text-xs font-medium text-gray-500 mb-1">Subject</label>
                <div className="relative">
                  <select
                    value={selectedSubject}
                    onChange={handleSubjectChange}
                    className="appearance-none bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-xl px-4 py-3 pr-10 text-sm font-medium text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                  >
                    {subjects.map((subject) => (
                      <option key={subject} value={subject}>
                        {subject}
                      </option>
                    ))}
                  </select>
                  <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-500" />
                </div>
              </div>
              
              <div className="relative">
                <label className="block text-xs font-medium text-gray-500 mb-1">Year</label>
                <div className="relative">
                  <select
                    value={selectedYear}
                    onChange={handleYearChange}
                    className="appearance-none bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-xl px-4 py-3 pr-10 text-sm font-medium text-gray-900 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all"
                  >
                    {years.map((year) => (
                      <option key={year} value={year}>
                        {year}
                      </option>
                    ))}
                  </select>
                  <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-500" />
                </div>
              </div>
            </div>

            {/* Stats Summary */}
            <div className="flex items-center space-x-6 ml-auto">
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{totalQuestions}</div>
                <div className="text-xs text-gray-500">Total Questions</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {chapters.filter(chapter => chapter.NEET[selectedYear] > 0).length}
                </div>
                <div className="text-xs text-gray-500">Active Chapters</div>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        {viewType === 'chart' ? (
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
            <div className="border-b border-gray-100 px-6 py-4">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-lg font-semibold text-gray-900">Question Distribution Analysis</h2>
                  <p className="text-sm text-gray-600">{selectedSubject} • NEET {selectedYear}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <TrendingUp className="w-5 h-5 text-green-500" />
                  <span className="text-sm text-gray-600">Visual Analytics</span>
                </div>
              </div>
            </div>
            
            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Pie Chart Section */}
                <div className="lg:col-span-2">
                  <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6">
                    <div className="h-[500px]">
                      <Pie data={pieChartData} options={pieChartOptions} />
                    </div>
                  </div>
                </div>

                {/* Chapter Details Section */}
                <div className="lg:col-span-1">
                  <h3 className="text-lg font-semibold mb-4 text-gray-900">Chapter Breakdown</h3>
                  <div className="bg-gray-50 rounded-xl p-4 h-[500px] overflow-y-auto">
                    <div className="space-y-3">
                      {chapters
                        .filter(chapter => chapter.NEET[selectedYear] > 0)
                        .sort((a, b) => (b.NEET[selectedYear] || 0) - (a.NEET[selectedYear] || 0))
                        .map((chapter, index) => {
                          const questions = chapter.NEET[selectedYear] || 0;
                          const percentage = totalQuestions > 0 ? ((questions / totalQuestions) * 100).toFixed(1) : 0;
                          const colors = generateColors(chapters.length);
                          const originalIndex = chapters.findIndex(c => c.Chapter_name === chapter.Chapter_name);
                          
                          return (
                            <div key={chapter.Chapter_name} className="bg-white rounded-lg p-4 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
                              <div className="flex items-center">
                                <div 
                                  className="w-4 h-4 rounded-full mr-3 flex-shrink-0 shadow-sm"
                                  style={{ backgroundColor: colors[originalIndex] }}
                                ></div>
                                <div className="flex-grow min-w-0">
                                  <p className="text-sm font-medium text-gray-900 truncate" title={chapter.Chapter_name}>
                                    {chapter.Chapter_name}
                                  </p>
                                  <div className="flex justify-between items-center mt-2">
                                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-md">
                                      {questions} questions
                                    </span>
                                    <span className="text-sm font-bold text-purple-600 bg-purple-50 px-2 py-1 rounded-md">
                                      {percentage}%
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
            <div className="border-b border-gray-100 px-6 py-4">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-lg font-semibold text-gray-900">Chapter-wise Question Table</h2>
                  <p className="text-sm text-gray-600">{selectedSubject} • NEET {selectedYear}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <Table className="w-5 h-5 text-blue-500" />
                  <span className="text-sm text-gray-600">Detailed View</span>
                </div>
              </div>
            </div>
            
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead className="bg-gradient-to-r from-purple-50 to-blue-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                      Chapter Name
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                      Questions (NEET {selectedYear})
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                      Percentage
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">
                      Priority
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-100">
                  {chapters.map((chapter, index) => {
                    const questions = chapter.NEET[selectedYear];
                    const percentage = totalQuestions > 0 && questions > 0 ? ((questions / totalQuestions) * 100).toFixed(1) : 0;
                    const getPriority = (pct) => {
                      if (pct >= 15) return { label: 'High', color: 'bg-red-100 text-red-800' };
                      if (pct >= 8) return { label: 'Medium', color: 'bg-yellow-100 text-yellow-800' };
                      if (pct > 0) return { label: 'Low', color: 'bg-green-100 text-green-800' };
                      return { label: 'None', color: 'bg-gray-100 text-gray-800' };
                    };
                    const priority = getPriority(parseFloat(percentage));
                    
                    return (
                      <tr key={index} className={`hover:bg-gray-50 transition-colors ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'}`}>
                        <td className="px-6 py-4 text-sm font-medium text-gray-900">
                          {chapter.Chapter_name}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-700">
                          {questions !== null && questions !== undefined ? (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {questions}
                            </span>
                          ) : (
                            <span className="text-gray-400">N/A</span>
                          )}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-700">
                          {percentage > 0 ? (
                            <span className="font-semibold text-purple-600">{percentage}%</span>
                          ) : (
                            <span className="text-gray-400">0%</span>
                          )}
                        </td>
                        <td className="px-6 py-4 text-sm">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${priority.color}`}>
                            {priority.label}
                          </span>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BoosterModule;