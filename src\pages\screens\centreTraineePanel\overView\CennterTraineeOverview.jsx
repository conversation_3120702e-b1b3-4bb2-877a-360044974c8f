import React, { useEffect, useState } from 'react';
import { useLazyGetCenterStudentsFacultyQuery } from './centerTraineedOverview.slice';
import { motion } from 'framer-motion';

const CenterTraineeOverview = () => {
  const [trigger, { data, error, isLoading }] = useLazyGetCenterStudentsFacultyQuery();
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredTeachers, setFilteredTeachers] = useState([]);
  // State variables
  const [facultySearchTerm, setFacultySearchTerm] = useState('');
  const [filteredFaculty, setFilteredFaculty] = useState(data?.other_faculty || []);

  // Handle faculty search
  useEffect(() => {
    if (!data?.other_faculty) return;

    if (facultySearchTerm === '') {
      setFilteredFaculty(data.other_faculty);
    } else {
      const results = data.other_faculty.filter(
        (faculty) =>
          `${faculty.first_name} ${faculty.last_name}`
            .toLowerCase()
            .includes(facultySearchTerm.toLowerCase()) ||
          (faculty.position &&
            faculty.position.toLowerCase().includes(facultySearchTerm.toLowerCase())) ||
          faculty.email.toLowerCase().includes(facultySearchTerm.toLowerCase()) ||
          (faculty.phone && faculty.phone.toLowerCase().includes(facultySearchTerm.toLowerCase()))
      );
      setFilteredFaculty(results);
    }
  }, [facultySearchTerm, data?.other_faculty]);

  // Initialize filtered faculty when data loads
  useEffect(() => {
    if (data?.other_faculty) {
      setFilteredFaculty(data.other_faculty);
    }
  }, [data?.other_faculty]);

  useEffect(() => {
    if (data?.kota_teachers) {
      setFilteredTeachers(data.kota_teachers);
    }
  }, [data]);

  // Handle search
  useEffect(() => {
    if (!data?.kota_teachers) return;

    if (searchTerm === '') {
      setFilteredTeachers(data.kota_teachers);
    } else {
      const results = data.kota_teachers.filter(
        (teacher) =>
          `${teacher.first_name} ${teacher.last_name}`
            .toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
          teacher.course.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (teacher.specialization &&
            teacher.specialization.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredTeachers(results);
    }
  }, [searchTerm, data?.kota_teachers]);

  useEffect(() => {
    trigger();
  }, [trigger]);

  if (isLoading) {
    return (
      <div className="relative flex flex-col items-center justify-center min-h-[300px] bg-white p-8 rounded-xl border border-gray-100">
        {/* Floating Nodes with Connection Paths */}
        <svg className="w-full h-48" viewBox="0 0 300 150" preserveAspectRatio="xMidYMid meet">
          {/* Dynamic connection lines */}
          <motion.path
            d="M30,75 Q150,20 270,75"
            stroke="var(--color-trainee)"
            strokeWidth={1.5}
            fill="none"
            strokeDasharray="0 1"
            animate={{
              strokeDashoffset: [0, -100]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: 'linear'
            }}
          />

          {/* Animated nodes */}
          {[
            { cx: 30, cy: 75, delay: 0 },
            { cx: 90, cy: 35, delay: 0.2 },
            { cx: 150, cy: 110, delay: 0.4 },
            { cx: 210, cy: 40, delay: 0.6 },
            { cx: 270, cy: 75, delay: 0.8 }
          ].map((node, i) => (
            <motion.circle
              key={i}
              cx={node.cx}
              cy={node.cy}
              r={8}
              fill="var(--color-trainee)"
              initial={{ opacity: 0, scale: 0 }}
              animate={{
                opacity: [0, 0.8, 0],
                scale: [0, 1.2, 0]
              }}
              transition={{
                duration: 2,
                delay: node.delay,
                repeat: Infinity,
                ease: 'easeInOut'
              }}
            />
          ))}
        </svg>

        {/* Progress Indicator */}
        <div className="w-full max-w-md mt-8">
          <div className="flex justify-between text-xs text-gray-500 mb-1">
            <span>Loading modules</span>
            <motion.span
              animate={{
                opacity: [0.5, 1, 0.5],
                x: [-2, 2, -2]
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity
              }}>
              ▸▸▸
            </motion.span>
          </div>
          <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
            <motion.div
              className="h-full rounded-full"
              style={{ backgroundColor: 'var(--color-trainee)' }}
              initial={{ width: 0 }}
              animate={{ width: ['0%', '90%', '100%'] }}
              transition={{
                duration: 2.5,
                repeat: Infinity,
                repeatType: 'reverse',
                ease: 'easeInOut'
              }}
            />
          </div>
        </div>

        {/* Status Text */}
        <motion.div
          className="mt-6 text-center"
          animate={{
            opacity: [0.8, 1, 0.8],
            y: [0, -2, 0]
          }}
          transition={{
            duration: 3,
            repeat: Infinity
          }}>
          <p className="text-sm font-medium text-gray-600">
            Initializing <span style={{ color: 'var(--color-trainee)' }}>dashboard</span>{' '}
            analytics...
          </p>
        </motion.div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50 p-4 md:p-8">
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full text-center border border-red-100">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg
                className="w-8 h-8 text-red-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-red-600 mb-2">Error Loading Data</h3>
            <p className="text-gray-600">
              {error.message || 'Something went wrong. Please try again.'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50 p-4 md:p-8">
      {/* Modern Header with Glass Morphism */}
      <div className="mb-8">
        <div className="bg-white/30 backdrop-blur-lg rounded-3xl p-6 md:p-8 shadow-xl border border-white/20 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-[#f59e0b]/10 to-orange-500/10"></div>
          <div className="absolute top-0 right-0 w-32 h-32 bg-white/20 rounded-full -translate-y-16 translate-x-16 filter blur-xl"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/20 rounded-full translate-y-12 -translate-x-12 filter blur-xl"></div>

          <div className="relative flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-6">
            <div className="w-20 h-20 bg-white/30 rounded-2xl flex items-center justify-center backdrop-blur-md border border-white/30 shadow-sm">
              <div className="w-14 h-14 bg-gradient-to-br from-[#f59e0b] to-orange-500 rounded-xl flex items-center justify-center shadow-md">
                <svg
                  className="w-8 h-8 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                  />
                </svg>
              </div>
            </div>
            <div className="text-center md:text-left">
              <h1 className="text-3xl md:text-4xl font-bold mb-2 tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-[#f59e0b] to-orange-600">
                Trainee Center Overview
              </h1>
              <p className="text-lg text-orange-700/90 font-medium">
                Comprehensive Center Management Dashboard
              </p>
            </div>
          </div>
        </div>
      </div>

      {data && (
        <div className="space-y-8">
          {/* Faculty Profile - Modern Dashboard Card */}
          <div className="bg-white rounded-2xl shadow-2xl overflow-hidden border border-white/20">
            {/* Profile Header with Glass Morphism */}
            <div className="relative  bg-[var(--color-trainee)] group-hover:bg-white p-6">
              <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
              <div className="relative flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <div className="w-16 h-16 bg-white/30 rounded-xl flex items-center justify-center backdrop-blur-md border-2 border-white/30 shadow-lg">
                      <div className="w-12 h-12 bg-gradient-to-br from-white to-white/80 rounded-lg flex items-center justify-center">
                        <span className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-[#f59e0b] to-orange-600">
                          {data.faculty.first_name.charAt(0)}
                          {data.faculty.last_name.charAt(0)}
                        </span>
                      </div>
                    </div>
                    <span
                      className={`absolute -bottom-1 -right-1 px-2 py-0.5 rounded-full text-xs font-bold ${
                        data.faculty.is_active ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
                      }`}>
                      {data.faculty.is_active ? '✓' : '✗'}
                    </span>
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-white drop-shadow-md">
                      {data.faculty.first_name} {data.faculty.last_name}
                    </h2>
                    <p className="text-orange-100 font-medium">{data.faculty.center_name}</p>
                  </div>
                </div>
                <div className="hidden md:block">
                  <div className="bg-white/20 px-4 py-2 rounded-full backdrop-blur-sm">
                    <span className="text-white font-medium">ID: {data.faculty.username}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Profile Content - Modern Dashboard Layout */}
            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Personal Info Card */}

                {/* Contact & Center Info Card */}
                <div className="lg:col-span-2">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Contact Card */}
                    <div className="bg-white rounded-xl border border-gray-100 shadow-sm p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-gray-800 flex items-center">
                          <svg
                            className="w-5 h-5 mr-2 text-orange-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                            />
                          </svg>
                          Personal Contact Details
                        </h3>
                        <div className="flex space-x-2">
                          <button className="p-1 text-gray-400 hover:text-orange-600 rounded-full hover:bg-orange-50">
                            <svg
                              className="w-5 h-5"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                              />
                            </svg>
                          </button>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div className="flex items-start">
                          <div className="flex-shrink-0 bg-orange-100 p-2 rounded-lg">
                            <svg
                              className="w-5 h-5 text-orange-600"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                              />
                            </svg>
                          </div>
                          <div className="ml-3">
                            <p className="text-xs font-medium text-gray-500 uppercase tracking-wider flex items-center">
                              Member Since
                            </p>
                            <div className="flex items-center justify-between">
                              <p className="text-gray-800 font-medium">
                                {new Date(data.faculty.created_at).toLocaleDateString('en-US', {
                                  year: 'numeric',
                                  month: 'long'
                                })}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div className="flex items-start">
                          <div className="flex-shrink-0 bg-orange-100 p-2 rounded-lg">
                            <svg
                              className="w-5 h-5 text-orange-600"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                              />
                            </svg>
                          </div>
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-500">Email</p>
                            <a
                              href={`mailto:${data.faculty.email}`}
                              className="text-base font-medium text-gray-900 hover:text-orange-600 transition-colors">
                              {data.faculty.email}
                            </a>
                          </div>
                        </div>

                        <div className="flex items-start">
                          <div className="flex-shrink-0 bg-orange-100 p-2 rounded-lg">
                            <svg
                              className="w-5 h-5 text-orange-600"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                              />
                            </svg>
                          </div>
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-500">Phone</p>
                            <a
                              href={`tel:${data.faculty.phone}`}
                              className="text-base font-medium text-gray-900 hover:text-orange-600 transition-colors">
                              {data.faculty.phone}
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Center Card */}
                    <div className="bg-white rounded-xl border border-gray-100 shadow-sm p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-gray-800 flex items-center">
                          <svg
                            className="w-5 h-5 mr-2 text-orange-500"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                            />
                          </svg>
                          Center Contact Details
                        </h3>
                        <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full">
                          #{data.faculty.center_code}
                        </span>
                      </div>

                      <div className="space-y-4">
                        <div className="flex items-start">
                          <div className="flex-shrink-0 bg-orange-100 p-2 rounded-lg">
                            <svg
                              className="w-5 h-5 text-orange-600"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                              />
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                              />
                            </svg>
                          </div>
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-500">Center</p>
                            <p className="text-base font-medium text-gray-900">
                              {data.faculty.center_name}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-start">
                          <div className="flex-shrink-0 bg-orange-100 p-2 rounded-lg">
                            <svg
                              className="w-5 h-5 text-orange-600"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                              />
                            </svg>
                          </div>
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-500">Center Email</p>
                            <a
                              href={`mailto:${data.faculty.center_email}`}
                              className="text-base font-medium text-gray-900 hover:text-orange-600 transition-colors">
                              {data.faculty.center_email}
                            </a>
                          </div>
                        </div>

                        <div className="flex items-start">
                          <div className="flex-shrink-0 bg-orange-100 p-2 rounded-lg">
                            <svg
                              className="w-5 h-5 text-orange-600"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                              />
                            </svg>
                          </div>
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-500">Center Phone</p>
                            <a
                              href={`tel:${data.faculty.center_phone}`}
                              className="text-base font-medium text-gray-900 hover:text-orange-600 transition-colors">
                              {data.faculty.center_phone}
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Kota Teachers Section - Column Layout */}
          <div className="space-y-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              {/* Header remains unchanged */}
              <div className="flex items-center">
                <h2 className="text-2xl font-bold text-black flex items-center">
                  <span
                    className="w-8 h-8 rounded-full flex items-center justify-center mr-3"
                    style={{ backgroundColor: 'var(--color-trainee)' }}>
                    <svg
                      className="w-5 h-5 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                      />
                    </svg>
                  </span>
                  Kota Teachers
                </h2>
                <span
                  className="ml-4 px-3 py-1 rounded-full text-sm font-medium"
                  style={{
                    backgroundColor: 'var(--color-trainee)',
                    color: 'white'
                  }}>
                  {filteredTeachers.length} Teachers
                </span>
              </div>

              <div className="relative w-full sm:w-64">
                <input
                  type="text"
                  placeholder="Search by name, course..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:outline-none"
                  style={{
                    backgroundColor: 'white',
                    color: 'black',
                    borderColor: 'var(--color-trainee)',
                    focusRingColor: 'var(--color-trainee)'
                  }}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <svg
                  className="w-5 h-5 absolute left-3 top-2.5"
                  fill="none"
                  stroke="var(--color-trainee)"
                  viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
            </div>

            {filteredTeachers.length > 0 ? (
              <div
                className="rounded-xl overflow-hidden border"
                style={{
                  backgroundColor: 'white',
                  borderColor: 'var(--color-trainee)'
                }}>
                <div className="overflow-x-auto">
                  <table
                    className="min-w-full divide-y"
                    style={{ borderColor: 'var(--color-trainee)' }}>
                    <thead>
                      <tr>
                        <th
                          scope="col"
                          className="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider relative"
                          style={{
                            background: 'var(--color-trainee)',
                            color: 'white',
                            clipPath:
                              'polygon(0 0, calc(100% - 15px) 0, 100% 15px, 100% 100%, 0 100%)'
                          }}>
                          <div className="flex items-center">
                            <svg
                              className="w-5 h-5 mr-2"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                              />
                            </svg>
                            <span>Teacher Profile</span>
                            <div className="absolute right-4 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-white rounded-full opacity-30"></div>
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider"
                          style={{
                            background: 'var(--color-trainee)',
                            color: 'white',
                            clipPath: 'polygon(15px 0, 100% 0, 100% 100%, 0 100%, 0 15px)'
                          }}>
                          <div className="flex items-center">
                            <svg
                              className="w-5 h-5 mr-2"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                              />
                            </svg>
                            <span>Subject</span>
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider"
                          style={{
                            background: 'var(--color-trainee)',
                            color: 'white',
                            clipPath:
                              'polygon(0 0, calc(100% - 15px) 0, 100% 15px, 100% 100%, 0 100%)'
                          }}>
                          <div className="flex items-center">
                            <svg
                              className="w-5 h-5 mr-2"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                              />
                            </svg>
                            <span>Courses</span>
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider"
                          style={{
                            background: 'var(--color-trainee)',
                            color: 'white',
                            clipPath: 'polygon(15px 0, 100% 0, 100% 100%, 0 100%, 0 15px)'
                          }}>
                          <div className="flex items-center">
                            <svg
                              className="w-5 h-5 mr-2"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                              />
                            </svg>
                            <span>Status</span>
                          </div>
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider"
                          style={{
                            background: 'var(--color-trainee)',
                            color: 'white',
                            clipPath:
                              'polygon(0 0, calc(100% - 15px) 0, 100% 15px, 100% 100%, 0 100%)'
                          }}>
                          <div className="flex items-center">
                            <svg
                              className="w-5 h-5 mr-2"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                              />
                            </svg>
                            <span>Performance</span>
                            <div className="absolute left-4 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-white rounded-full opacity-30"></div>
                          </div>
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y" style={{ borderColor: 'var(--color-trainee)' }}>
                      {filteredTeachers.map((teacher, index) => (
                        <tr key={index} className="hover:bg-gray-50 transition-colors duration-150">
                          <td className="px-6 py-5">
                            <div className="flex items-center">
                              <div
                                className="flex-shrink-0 w-12 h-12 rounded-xl flex items-center justify-center text-white font-bold text-lg shadow-md"
                                style={{ backgroundColor: 'var(--color-trainee)' }}>
                                {teacher.first_name.charAt(0)}
                                {teacher.last_name.charAt(0)}
                              </div>
                              <div className="ml-4">
                                <div className="text-lg font-semibold text-black">
                                  {teacher.first_name} {teacher.last_name}
                                  {teacher.is_lead && (
                                    <span
                                      className="ml-2 px-2 py-0.5 text-xs font-bold rounded-full"
                                      style={{
                                        backgroundColor: 'var(--color-trainee)',
                                        color: 'white'
                                      }}>
                                      Lead
                                    </span>
                                  )}
                                </div>
                                <div className="text-sm text-gray-700 mt-1">
                                  <span className="flex items-center">
                                    <svg
                                      className="w-4 h-4 mr-1"
                                      fill="none"
                                      stroke="var(--color-trainee)"
                                      viewBox="0 0 24 24">
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                                      />
                                    </svg>
                                    {teacher.email}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-5">
                            <span className="text-sm font-semibold text-black">
                              {teacher.subject}
                            </span>
                          </td>
                          <td className="px-6 py-5">
                            <div className="flex flex-col space-y-2">
                              <span
                                className="inline-block px-3 py-1 text-sm font-semibold rounded-full"
                                style={{
                                  backgroundColor: 'var(--color-trainee)',
                                  color: 'white'
                                }}>
                                {teacher.course}
                              </span>
                              {teacher.specialization && (
                                <span className="inline-block px-3 py-1 text-sm font-medium rounded-full bg-gray-100 text-gray-800">
                                  {teacher.specialization}
                                </span>
                              )}
                            </div>
                          </td>
                          <td className="px-6 py-5">
                            <div className="flex items-center">
                              <div
                                className={`w-3 h-3 rounded-full mr-2 ${teacher.available ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                              <span className="text-sm font-medium text-black">
                                {teacher.available ? 'Available' : 'On Leave'}
                              </span>
                            </div>
                            {teacher.schedule && (
                              <div className="mt-2 text-sm text-gray-700">
                                <span className="font-medium">Schedule:</span> {teacher.schedule}
                              </div>
                            )}
                          </td>
                          <td className="px-6 py-5">
                            <div className="flex items-center">
                              <div
                                className="w-24 bg-gray-200 rounded-full h-2.5 mr-3"
                                style={{ backgroundColor: 'rgba(var(--color-trainee-rgb), 0.2)' }}>
                                <div
                                  className={`h-2.5 rounded-full`}
                                  style={{
                                    width: `${teacher.performance_score}%`,
                                    backgroundColor: 'var(--color-trainee)'
                                  }}></div>
                              </div>
                              <span className="text-sm font-medium text-black">
                                {teacher.performance_score}%
                              </span>
                            </div>
                            <div className="mt-2 text-xs text-gray-600">
                              Last evaluated: {teacher.last_evaluation || 'N/A'}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            ) : (
              /* Empty state remains unchanged */
              <div
                className="rounded-xl p-8 text-center border-2 border-dashed"
                style={{
                  backgroundColor: 'white',
                  borderColor: 'var(--color-trainee)'
                }}>
                <svg
                  className="w-12 h-12 mx-auto"
                  fill="none"
                  stroke="var(--color-trainee)"
                  viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                  />
                </svg>
                <h3 className="text-lg font-medium text-black mt-4">
                  {searchTerm ? 'No matching teachers found' : 'No Kota Teachers Found'}
                </h3>
                <p className="text-gray-700 mt-2">
                  {searchTerm
                    ? 'Try adjusting your search query'
                    : 'There are currently no Kota teachers assigned to this center.'}
                </p>
                {!searchTerm && (
                  <button
                    className="mt-4 inline-flex items-center px-4 py-2 rounded-lg transition-colors"
                    style={{
                      backgroundColor: 'var(--color-trainee)',
                      color: 'white'
                    }}
                    onClick={() => {
                      // TODO: Implement add teacher functionality
                      console.log('Add teacher clicked');
                    }}>
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                      />
                    </svg>
                    Add Teacher
                  </button>
                )}
              </div>
            )}
          </div>

          {/* Other Faculty Section - Column Layout */}
          <div className="space-y-6">
            {/* Header Section with Search */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-4 bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-amber-500 rounded-lg flex items-center justify-center shadow-md">
                  <svg
                    className="w-6 h-6 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                    />
                  </svg>
                </div>
                <div className="ml-4">
                  <h2 className="text-xl font-bold text-gray-800">Other Traine Members</h2>
                  <p className="text-sm text-amber-700">Supporting staff</p>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
                {/* Search Bar - Integrated Design */}
                <div className="relative flex-1 min-w-[200px]">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg
                      className="w-4 h-4 text-amber-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                      />
                    </svg>
                  </div>
                  <input
                    type="text"
                    placeholder="Search faculty..."
                    className="block w-full pl-9 pr-3 py-2 text-sm border border-amber-200 rounded-lg bg-white focus:ring-2 focus:ring-amber-300 focus:border-amber-300"
                    value={facultySearchTerm}
                    onChange={(e) => setFacultySearchTerm(e.target.value)}
                  />
                </div>

                <div className="flex items-center justify-between sm:justify-end gap-3">
                  <div className="bg-white px-3 py-1 rounded-full shadow-sm flex items-center">
                    <span className="w-2 h-2 bg-amber-500 rounded-full mr-2"></span>
                    <span className="text-sm font-medium text-gray-700">
                      {filteredFaculty.length} Members
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Faculty Cards */}
            {filteredFaculty.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-5">
                {filteredFaculty.map((faculty, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    whileHover={{
                      x: 10, // Slide right on hover
                      transition: { duration: 0.3 }
                    }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className="relative bg-white  rounded-xl shadow-sm border border-gray-100 overflow-hidden group cursor-pointer"
                    style={{
                      '--color-trainee': '#f59e0b',
                      '--color-trainee-dark': '#d97706',
                      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
                    }}>
                    {/* **SLIDE SWIPE BACKGROUND EFFECT** */}
                    <motion.div
                      className="absolute inset-0 z-0"
                      initial={{ x: 0 }}
                      whileHover={{
                        x: -20, // Pulls background left to reveal gradient
                        transition: { duration: 0.3 }
                      }}>
                      <div
                        className="absolute inset-0 opacity-0 group-hover:opacity-100  transition-opacity duration-300"
                        style={{
                          background:
                            'linear-gradient(90deg, transparent 60%, var(--color-trainee) 100%)',
                          filter: 'blur(1px)'
                        }}
                      />
                    </motion.div>
                    {/* Left border shadow effect */}
                    <motion.div
                      className="absolute left-0  top-0 h-full w-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                      style={{
                        background:
                          'linear-gradient(to bottom, transparent, var(--color-trainee), transparent)',
                        boxShadow: '0 0 12px 2px var(--color-trainee)'
                      }}
                    />

                    {/* Slide swipe effect container */}
                    <motion.div
                      className="absolute inset-0 z-0"
                      whileHover={{
                        x: -10, // Opposite direction for layered effect
                        transition: { duration: 0.3 }
                      }}>
                      {/* Swipe background */}
                      <div
                        className="absolute right-0 top-0 w-full  h-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                        style={{
                          background:
                            'linear-gradient(90deg, transparent 70%, var(--color-trainee) 100%)'
                        }}
                      />
                    </motion.div>

                    {/* Card content */}
                    <div className="relative z-10 bg-white hover:bg-[var(--color-trainee)]  h-full">
                      {/* Profile Header */}
                      <div className="relative">
                        <div className="h-8" style={{ backgroundColor: 'var(--color-trainee)' }} />
                        <div className="absolute -bottom-8 right-3 w-16 h-16 bg-white rounded-full border-4 border-white shadow-lg overflow-hidden">
                          <div
                            className="w-full h-full flex items-center justify-center text-white font-bold text-xl"
                            style={{ backgroundColor: 'var(--color-trainee)' }}>
                            {faculty.first_name.charAt(0)}
                            {faculty.last_name.charAt(0)}
                          </div>
                        </div>
                      </div>

                      {/* Faculty Info */}
                      <div className="pt-10 px-5  pb-5">
                        <h3 className="font-bold text-gray-800 text-lg  transition-colors duration-300 group-hover:text-white">
                          {faculty.first_name} {faculty.last_name}
                        </h3>

                        {faculty.position && (
                          <p
                            className="text-sm font-medium mt-1 inline-block"
                            style={{ color: 'var(--color-trainee)' }}>
                            {faculty.position}
                          </p>
                        )}

                        <div className="mt-4 space-y-3">
                          <div className="flex items-start">
                            <svg
                              className="w-5 h-5 mt-0.5 mr-2 group-hover:bg-white   flex-shrink-0"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                              style={{ color: 'var(--color-trainee)' }}>
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={1.5}
                                d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                              />
                            </svg>
                            <span className="text-sm text-gray-600 group-hover:text-white break-all">
                              {faculty.email}
                            </span>
                          </div>

                          <div className="flex group-hover:text-white items-center">
                            <svg
                              className="w-5 h-5 mr-2 group-hover:bg-white flex-shrink-0"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                              style={{ color: 'var(--color-trainee)' }}>
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={1.5}
                                d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                              />
                            </svg>
                            <span className="text-sm group-hover:text-white text-gray-600">
                              {faculty.phone}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Right shadow effect on hover */}
                    <motion.div
                      className="absolute inset-0 rounded-xl pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                      style={{
                        boxShadow: '12px 0 20px -8px var(--color-trainee-dark)',
                        left: '-12px'
                      }}
                    />
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="bg-gradient-to-br from-orange-50 to-amber-50 rounded-xl p-8 text-center border-2 border-dashed border-amber-200">
                <div className="w-20 h-20 mx-auto bg-gradient-to-br from-orange-100 to-amber-100 rounded-full flex items-center justify-center shadow-inner">
                  <svg
                    className="w-10 h-10 text-amber-500"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1.5}
                      d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                    />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-gray-800 mt-5">
                  {facultySearchTerm ? 'No matching members found' : 'No Faculty Members'}
                </h3>
                <p className="text-gray-600 mt-2 max-w-md mx-auto">
                  {facultySearchTerm
                    ? 'Try adjusting your search query'
                    : 'There are currently no faculty members assigned to this center.'}
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CenterTraineeOverview;
