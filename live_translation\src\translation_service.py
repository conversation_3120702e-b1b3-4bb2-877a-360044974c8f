# --- START OF FINAL "PERFECT CLARITY" MASTER FILE: translation_service.py ---

import base64
import os
import asyncio
import logging
import json 
from typing import Optional, Dict, Any, Tuple

from openai import AsyncOpenAI
from google.cloud import texttospeech_v1 as texttospeech

logger = logging.getLogger(__name__)

class TranslationService:
    def __init__(self):
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.openai_client = None
        self.google_tts_client = None
        self.google_tts_voice_map: Dict[str, Tuple[str, str]] = {
            "en": ("en-IN", os.getenv("TTS_VOICE_EN", "en-IN-Chirp3-HD-Vindemiatrix")),
            "ta": ("ta-IN", os.getenv("TTS_VOICE_TA", "ta-IN-Chirp3-HD-Vindemiatrix")),
            "hi": ("hi-IN", os.getenv("TTS_VOICE_HI", "hi-IN-Chirp3-HD-Vindemiatrix")),
            "te": ("te-IN", os.getenv("TTS_VOICE_TE", "te-IN-Chirp3-HD-Vindemiatrix")),
            "kn": ("kn-IN", os.getenv("TTS_VOICE_KN", "kn-IN-Chirp3-HD-Vindemiatrix")),
            "ml": ("ml-IN", os.getenv("TTS_VOICE_ML", "ml-IN-Chirp3-HD-Vindemiatrix")),
            "bn": ("bn-IN", os.getenv("TTS_VOICE_BN", "bn-IN-Chirp3-HD-Vindemiatrix")),
            "default": ("en-IN", os.getenv("TTS_VOICE_EN", "en-IN-Chirp3-HD-Vindemiatrix"))
        }
        self.supported_languages = {
            "en": {"name": "English"}, "ta": {"name": "Tamil"}, "hi": {"name": "Hindi"},
            "te": {"name": "Telugu"}, "kn": {"name": "Kannada"}, "ml": {"name": "Malayalam"},
            "es": {"name": "Spanish"}, "fr": {"name": "French"}, "bn": {"name": "Bengali"},
        }
        self.language_full_names = {code: data["name"] for code, data in self.supported_languages.items()}

    async def initialize(self):
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY must be configured.")
        self.openai_client = AsyncOpenAI(api_key=self.openai_api_key)
        try:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            credentials_path = os.path.join(script_dir, "gcp-credentials.json")
            if not os.path.exists(credentials_path):
                raise FileNotFoundError(f"CRITICAL: 'gcp-credentials.json' not found at '{credentials_path}'")
            self.google_tts_client = texttospeech.TextToSpeechAsyncClient.from_service_account_file(credentials_path)
            logger.info("✅ Perfect Clarity Master Prompt TranslationService initialized.")
        except Exception as e:
            logger.error(f"Failed to initialize a service: {e}", exc_info=True)
            raise

    async def speech_to_text(self, audio_data: bytes, language: str, prompt: str = "") -> Optional[str]:
        if not audio_data or len(audio_data) < 1000: return None
        try:
            response = await self.openai_client.audio.transcriptions.create(
                model="whisper-1", 
                file=("audio.wav", audio_data),
                language=language,
                prompt=prompt
            )
            transcript = response.text.strip()
            if transcript and len(transcript) > 1 and "transcribe technical terms" not in transcript.lower():
                logger.info(f"🎤 Whisper STT: '{transcript}'")
                return transcript
            else:
                logger.warning(f"Whisper filtered out potential hallucination: '{transcript}'")
                return None
        except Exception as e:
            logger.error(f"Error in speech-to-text: {e}", exc_info=True)
            return None

    async def translate_text(self, text: str, source_lang: str, target_lang: str) -> Optional[Dict[str, Any]]:
        if not text or not self.openai_client: return None
        
        source_name = self.language_full_names.get(source_lang, "Mixed-Language")
        target_name = self.language_full_names.get(target_lang, target_lang)

        # --- THE PERFECT CLARITY MASTER PROMPT ---
        prompt = (
            f"You are an AI assistant for a live NEET/JEE coaching class. Your role is to translate a teacher's lecture from **{source_name}** into natural, code-mixed **{target_name}**, mimicking the exact style of a top teacher in that region.\n\n"
            "### **PRIMARY DIRECTIVE: Create a Flawless, Natural, and Phonetically Perfect Translation.**\n\n"
            "--- **CORE METHODOLOGY** ---\n"
            "1.  **Analyze and Normalize (Internal Step):** The teacher's input might be mixed-language. First, mentally convert the source text into its **Standard Pure English** equivalent to understand the core concept.\n"
            "2.  **Translate with Style Emulation:** Generate a translation that sounds completely natural for a student in that region. For Tamil, this means using conversational words like `அதுல`, `-ஓட`, `பண்ணினா`, `இருக்கு`. For Hindi, use a similar natural style.\n\n"
            "--- **NON-NEGOTIABLE RULES for the FINAL OUTPUT** ---\n"
            "1.  **LITERAL MEANING, NATURAL STYLE:** Translate ONLY the meaning present in the source text. Do not add facts. The output must be grammatically correct and stylistically natural.\n"
            "2.  **INTELLIGENT CODE-MIXING:** Keep English technical terms (`combustion`, `energy`, `DNA`, etc.) in English. Translate the connecting grammar and non-technical words around them.\n\n"
            "--- **JSON OUTPUT SPECIFICATION (Crystal-Clear and Strictly Enforced)** ---\n"
            "Your response must be a single, valid JSON object with ONLY these two keys:\n"
            "1.  **`displayText`**: The text for subtitles. This MUST be a clean, code-mixed sentence. **It MUST NOT contain hyphens or suffixes attached to English words.** It should look like this: `Combustion ஒரு chemical process தான்...`.\n"
            "2.  **`ttsText`**: The text for the speech engine. This MUST be a **phonetically perfect, seamless sentence entirely in the target language's script (Tamil, Devanagari, etc.).** Combine the pronunciation of English words with the native words.\n"
            "    - English `Combustion` becomes Tamil `கம்பஸ்ஷன்`.\n"
            "    - English `DNA` becomes Tamil `டிஎன்ஏ`.\n"
            "    - The `displayText` `DNA carries information` becomes the `ttsText` `டிஎன்ஏ இன்ஃபர்மேஷனை கேரி செய்கிறது`.\n\n"
            "--- **PERFECT STYLE EXAMPLE (English to Tanglish)** ---\n"
            "**Source:** \"DNA carries the information that tells your body how to grow.\"\n"
            "**Correct JSON Output:**\n"
            "```json\n"
            "{\n"
            "  \"displayText\": \"DNA information-ஐ carry செய்கிறது, அது உங்கள் body எப்படி grow ஆகணும்-னு சொல்லும்.\",\n"
            "  \"ttsText\": \"டிஎன்ஏ இன்ஃபர்மேஷனை கேரி செய்கிறது, அது உங்கள் பாடி எப்படி க்ரோ ஆகணும்னு சொல்லும்.\"\n"
            "}\n"
            "```\n\n"
            f"You are now the AI assistant. Process the following text from the teacher into the target language **{target_name}**, adhering to all rules perfectly.\n"
            f"**Source Text: \"{text}\"**"
        )
        
        try:
            response = await self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                response_format={"type": "json_object"}
            )
            response_content = response.choices[0].message.content
            translation_data = json.loads(response_content)
            
            if "displayText" in translation_data and "ttsText" in translation_data:
                logger.info(f"🌐 AI Perfect Clarity Result:\n--- Source: '{text}'\n--- Display: '{translation_data['displayText']}'\n--- TTS: '{translation_data['ttsText']}'")
                return translation_data
            
            logger.warning(f"Translation JSON from OpenAI missing required keys for source: '{text}'")
            return None
        except Exception as e:
            logger.error(f"Error during OpenAI translation: {e}", exc_info=True)
            return None

    async def text_to_speech(self, text: str, language: str) -> Optional[str]:
        if not text:
            logger.warning("🔊 TTS: Empty text provided for TTS generation")
            return None
        if not self.google_tts_client:
            logger.error("🔊 TTS: Google TTS client not initialized")
            return None

        lang_code, voice_name = self.google_tts_voice_map.get(language, ("en-US", "en-US-Wavenet-D"))
        logger.info(f"🔊 TTS: Generating audio for language '{language}' -> {lang_code}, voice: {voice_name}")
        logger.info(f"🔊 TTS: Text to synthesize (first 100 chars): '{text[:100]}{'...' if len(text) > 100 else ''}'")

        try:
            synthesis_input = texttospeech.SynthesisInput(text=text)
            voice = texttospeech.VoiceSelectionParams(language_code=lang_code, name=voice_name)
            audio_config = texttospeech.AudioConfig(audio_encoding=texttospeech.AudioEncoding.MP3)

            logger.info(f"🔊 TTS: Sending request to Google TTS service...")
            response = await self.google_tts_client.synthesize_speech(input=synthesis_input, voice=voice, audio_config=audio_config)
            audio_bytes = response.audio_content

            if audio_bytes:
                audio_size = len(audio_bytes)
                logger.info(f"🔊 TTS: Successfully generated audio - Size: {audio_size} bytes")
                audio_b64 = base64.b64encode(audio_bytes).decode('utf-8')
                logger.info(f"🔊 TTS: Base64 encoded audio - Size: {len(audio_b64)} characters")
                return audio_b64
            else:
                logger.warning("🔊 TTS: Google TTS returned empty audio content")
                return None
        except Exception as e:
            logger.error(f"🔊 TTS: Error during TTS generation: {e}", exc_info=True)
            return None
