import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faHome,
  faUserPlus,
  faChalkboardTeacher,
  faUsers,
  faUserGraduate,
  faBook,
  faBars,
  faTimes
} from '@fortawesome/free-solid-svg-icons';

import Overview from '../overView/Overview';
import AddStudents from '../addStudents/AddStudents';
import AddFaculty from '../addFaculty/AddFaculty';
import ListStudents from '../listStudents/ListStudents';
import ListFaculty from '../listFaculty/ListFaculty';
import KotaTeachers from '../kotaTeachers/KotaTeachers';
import {
  useLazyGetCentreCounselorDashboardQuery,
  useLazyGetListFacultyQuery,
  useLazyGetListKotaTeachersQuery,
  useLazyGetListStudentsQuery
} from './centreCounselorDashboard.slice';
import Toastify from '../../../../components/PopUp/Toastify';

const CentreCounselorDashboard = () => {
  const [activeView, setActiveView] = useState('overview');

  const [centerInfo, setCenterInfo] = useState(null);
  const [students, setStudents] = useState([]);
  const [faculty, setFaculty] = useState([]);
  const [kotaTeachers, setKotaTeachers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showLanding, setShowLanding] = useState(true);
  const [isSidebarExpanded, setIsSidebarExpanded] = useState(false);
  const [CenterCounselorData] = useLazyGetCentreCounselorDashboardQuery();
  const [getListFaculty] = useLazyGetListFacultyQuery();
  const [getListStudents] = useLazyGetListStudentsQuery();
  const [getListKotaTeachers] = useLazyGetListKotaTeachersQuery();
  // const sidebarItems = [
  //   {
  //     label: 'Overview',
  //     icon: faHome,
  //     view: 'overview',
  //     onClick: () => setActiveView('overview'),
  //     active: activeView === 'overview'
  //   },
  //   {
  //     label: 'Add Students',
  //     icon: faUserPlus,
  //     view: 'add-students',
  //     onClick: () => setActiveView('add-students'),
  //     active: activeView === 'add-students'
  //   },
  //   {
  //     label: 'Add Faculty',
  //     icon: faChalkboardTeacher,
  //     view: 'add-faculty',
  //     onClick: () => setActiveView('add-faculty'),
  //     active: activeView === 'add-faculty'
  //   },
  //   {
  //     label: 'List Students',
  //     icon: faUsers,
  //     view: 'list-students',
  //     onClick: () => setActiveView('list-students'),
  //     active: activeView === 'list-students'
  //   },
  //   {
  //     label: 'List Faculty',
  //     icon: faUserGraduate,
  //     view: 'list-faculty',
  //     onClick: () => setActiveView('list-faculty'),
  //     active: activeView === 'list-faculty'
  //   },
  //   {
  //     label: 'Kota Teachers',
  //     icon: faBook,
  //     view: 'kota-teachers',
  //     onClick: () => setActiveView('kota-teachers'),
  //     active: activeView === 'kota-teachers'
  //   }
  // ];
  const [res, setRes] = useState(null);
  useEffect(() => {
    const timer = setTimeout(() => setShowLanding(false), 3000);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    fetchCenterInfo();
    if (activeView === 'list-students') fetchStudents();
    else if (activeView === 'list-faculty') fetchFaculty();
    else if (activeView === 'kota-teachers') fetchKotaTeachers();
  }, [activeView]);

  const fetchCenterInfo = async () => {
    try {
      const result = await CenterCounselorData().unwrap();
      setCenterInfo(result.center);
      setRes(result);
    } catch (error) {
      setRes(error);
      console.error('Failed to fetch center info:', error);
    }
  };

  const fetchStudents = async () => {
    try {
      const result = await getListStudents().unwrap();
      setStudents(result.students);
      setRes(result);
    } catch (error) {
      setRes(error);
      console.error('Failed to fetch students:', error);
    }
  };

  const fetchFaculty = async () => {
    try {
      const result = await getListFaculty().unwrap();
      setFaculty(result.faculty);
      setRes(result);
    } catch (error) {
      setRes(error);
      console.error('Failed to fetch faculty:', error);
    }
  };

  const fetchKotaTeachers = async () => {
    try {
      const result = await getListKotaTeachers().unwrap();
      setKotaTeachers(result.kota_teachers);
      setRes(result);
    } catch (error) {
      setRes(error);
      console.error('Failed to fetch Kota teachers:', error);
    }
  };

  // New landing page animation variants
  const containerVariants = {
    initial: { opacity: 0 },
    animate: {
      opacity: 1,
      transition: { duration: 1, staggerChildren: 0.3 }
    },
    exit: { opacity: 0, transition: { duration: 0.5 } }
  };

  const itemVariants = {
    initial: { opacity: 0, y: 30 },
    animate: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.8, ease: 'easeOut' }
    }
  };

  return (
    <div className="relative min-h-screen">
      <Toastify res={res} resClear={() => setRes(null)} />
      {/* New Landing Page */}
      <AnimatePresence>
        {showLanding && (
          <motion.div
            className="fixed inset-0 z-50 bg-gradient-to-br from-blue-500 to-purple-600 flex flex-col items-center justify-center"
            variants={containerVariants}
            initial="initial"
            animate="animate"
            exit="exit"
          >
            <motion.div className="text-center space-y-6" variants={itemVariants}>
              <motion.h1
                className="text-5xl md:text-6xl font-extrabold text-white drop-shadow-lg"
                variants={itemVariants}
              >
                Dashboard Access
              </motion.h1>
              <motion.p className="text-xl text-white/90 max-w-md mx-auto" variants={itemVariants}>
                Manage your center efficiently as a Counselor
              </motion.p>
              <motion.div
                className="w-16 h-16 mx-auto border-4 border-white rounded-full animate-spin"
                variants={itemVariants}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Dashboard */}
      
    </div>
  );
};

export default CentreCounselorDashboard;
